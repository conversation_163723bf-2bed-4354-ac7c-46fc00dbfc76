package device

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	authorizer "synapse-its.com/shared/api/authorizer"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

func TestHandler(t *testing.T) {
	// Backup overrides
	origUserInfo := userInfoFromContext
	origParse := parseRequest
	origGetPG := getPgDeviceInfo
	origGetRD := getRedisDeviceStatus
	defer func() {
		userInfoFromContext = origUserInfo
		parseRequest = origParse
		getPgDeviceInfo = origGetPG
		getRedisDeviceStatus = origGetRD
	}()

	tests := []struct {
		name        string
		userCtxOk   bool
		parseErr    error
		pgErr       error
		rdErr       error
		injectConns bool
		wantStatus  int
	}{
		{"no user info", false, nil, nil, nil, true, http.StatusInternalServerError},
		{"parse error", true, errors.New("parse fail"), nil, nil, true, http.StatusUnauthorized},
		{"connection error", true, nil, nil, nil, false, http.StatusInternalServerError},
		{"pg error", true, nil, errors.New("pg fail"), nil, true, http.StatusInternalServerError},
		{"redis error", true, nil, nil, errors.New("redis fail"), true, http.StatusInternalServerError},
		{"success", true, nil, nil, nil, true, http.StatusOK},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// override authorizer
			userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
				if tc.userCtxOk {
					return &authorizer.UserInfo{ID: 1}, true
				}
				return nil, false
			}

			// override parseRequest
			parseRequest = func(r *http.Request) (bool, int, error) {
				return true, 0, tc.parseErr
			}

			// override getPgDeviceInfo
			getPgDeviceInfo = func(pg connect.DatabaseExecutor, ui *authorizer.UserInfo, allDevices bool, deviceId int) (*[]dataPayload, []string, error) {
				if tc.pgErr != nil {
					return nil, nil, tc.pgErr
				}
				dummy := []dataPayload{{}}
				return &dummy, []string{"gw"}, nil
			}

			// override getRedisDeviceStatus
			getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, info *[]dataPayload, gids []string) (*[]dataPayload, error) {
				if tc.rdErr != nil {
					return nil, tc.rdErr
				}
				return info, nil
			}

			// prepare context
			var ctx context.Context
			if tc.injectConns {
				ctx = connect.WithConnections(context.Background(), mocks.FakeConns())
			} else {
				ctx = context.Background()
			}
			req := httptest.NewRequest(http.MethodGet, "/devices", nil).WithContext(ctx)
			rr := httptest.NewRecorder()

			// call handler
			Handler(rr, req)

			// verify status code
			if rr.Code != tc.wantStatus {
				t.Errorf("%s: status = %d; want %d", tc.name, rr.Code, tc.wantStatus)
			}
		})
	}
}

func TestParseRequest(t *testing.T) {
	tests := []struct {
		name        string
		queryParams map[string]string
		wantAll     bool
		wantID      int
		wantErr     bool
		errContains string
	}{
		{
			name:        "no params",
			queryParams: nil,
			wantAll:     true,
			wantID:      0,
			wantErr:     false,
		},
		{
			name:        "valid deviceid",
			queryParams: map[string]string{"deviceid": "123"},
			wantAll:     false,
			wantID:      123,
			wantErr:     false,
		},
		{
			name:        "empty deviceid param",
			queryParams: map[string]string{"deviceid": ""},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrInvalidUrlQuery),
		},
		{
			name:        "non-int deviceid",
			queryParams: map[string]string{"deviceid": "abc"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrConvertQueryParam),
		},
		{
			name:        "non-int deviceid, starting with int",
			queryParams: map[string]string{"deviceid": "1a"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrConvertQueryParam),
		},
		{
			name:        "too many params",
			queryParams: map[string]string{"a": "1", "b": "2"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: ErrUnexpectedUsage.Error(),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// build request
			values := url.Values{}
			for k, v := range tc.queryParams {
				values.Set(k, v)
			}
			r := &http.Request{URL: &url.URL{RawQuery: values.Encode()}}

			// call parseRequest
			all, id, err := parseRequest(r)

			// check error
			if tc.wantErr {
				if err == nil {
					t.Fatalf("%s: expected error, got none", tc.name)
				}
				if tc.errContains != "" && !strings.Contains(err.Error(), tc.errContains) {
					t.Errorf("%s: error = %q, want contains %q", tc.name, err.Error(), tc.errContains)
				}
				return
			}
			if err != nil {
				t.Fatalf("%s: unexpected error: %v", tc.name, err)
			}

			// check allDevices and id
			if all != tc.wantAll {
				t.Errorf("%s: allDevices = %v; want %v", tc.name, all, tc.wantAll)
			}
			if id != tc.wantID {
				t.Errorf("%s: deviceId = %d; want %d", tc.name, id, tc.wantID)
			}
		})
	}
}

func TestGetPgDeviceInfo(t *testing.T) {
	user := &authorizer.UserInfo{ID: 7}

	tests := []struct {
		name            string
		allDevices      bool
		deviceId        int
		seedDevices     []pgDeviceInfo
		wantErr         bool
		wantGatewayIds  []string
		wantPayloadLen  int
		wantFilterInSQL bool
	}{
		{
			name:        "db error allDevices",
			allDevices:  true,
			seedDevices: nil,
			wantErr:     true,
		},
		{
			name:        "db error filtered",
			allDevices:  false,
			deviceId:    42,
			seedDevices: nil,
			wantErr:     true,
		},
		{
			name:       "normal allDevices unique gateways",
			allDevices: true,
			seedDevices: []pgDeviceInfo{
				{SoftwareGatewayIdentifier: "a"},
				{SoftwareGatewayIdentifier: "b"},
				{SoftwareGatewayIdentifier: "a"},
			},
			wantErr:         false,
			wantGatewayIds:  []string{"GatewayRMSData:a", "GatewayRMSData:b"},
			wantPayloadLen:  3,
			wantFilterInSQL: false,
		},
		{
			name:       "normal filtered duplicate gateways",
			allDevices: false,
			deviceId:   55,
			seedDevices: []pgDeviceInfo{
				{SoftwareGatewayIdentifier: "x"},
				{SoftwareGatewayIdentifier: "x"},
			},
			wantErr:         false,
			wantGatewayIds:  []string{"GatewayRMSData:x"},
			wantPayloadLen:  2,
			wantFilterInSQL: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// capture query
			var gotQuery string

			// arrange FakeDBExecutor
			db := &mocks.FakeDBExecutor{}
			db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				gotQuery = query
				// simulate error when seedDevices is nil => db error
				if tc.seedDevices == nil {
					return errors.New("db failure")
				}
				// populate dest
				*(dest.(*[]pgDeviceInfo)) = tc.seedDevices
				return nil
			}

			// act
			payloads, gatewayIds, err := getPgDeviceInfo(db, user, tc.allDevices, tc.deviceId)

			// assert errors
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got nil")
				}
				return
			} else if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			// assert SQL filter presence
			hasFilter := strings.Contains(gotQuery, "AND ud.DeviceId =")
			if hasFilter != tc.wantFilterInSQL {
				t.Errorf("filter in SQL = %v; want %v", hasFilter, tc.wantFilterInSQL)
			}

			// assert payload length
			if payloads == nil {
				t.Fatalf("payloads is nil; want non-nil")
			}
			if len(*payloads) != tc.wantPayloadLen {
				t.Errorf("payload len = %d; want %d", len(*payloads), tc.wantPayloadLen)
			}

			// assert gateway IDs
			if !reflect.DeepEqual(gatewayIds, tc.wantGatewayIds) {
				t.Errorf("gatewayIds = %v; want %v", gatewayIds, tc.wantGatewayIds)
			}
		})
	}
}

func TestGetRedisDeviceStatus(t *testing.T) {
	// Backup original ProcessRmsData
	origProc := deviceProcessRmsData
	defer func() { deviceProcessRmsData = origProc }()

	tests := []struct {
		name       string
		info       *[]dataPayload
		gateways   []string
		mockSetup  func(redismock.ClientMock)
		processErr bool
		wantErr    bool
		wantNil    bool
		wantLen    int
	}{
		{
			name:      "nil info",
			info:      nil,
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty info",
			info:      &[]dataPayload{},
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty gateways",
			info:      &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways:  []string{},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:     "mget error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetErr(errors.New("mget fail"))
			},
			wantErr: true,
		},
		{
			name:     "raw nil and wrong type",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k1", "k2"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k1", "k2").SetVal([]interface{}{nil, 123})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "json unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetVal([]interface{}{`not-json`})
			},
			wantErr: true,
		},
		{
			name:     "base64 decode error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: "bad!"})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "proto unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				raw := base64.StdEncoding.EncodeToString([]byte("no-proto"))
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: raw})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "missing device id",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "other", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "process error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				// same as valid path
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			processErr: true,
			wantErr:    false,
			wantNil:    false,
			wantLen:    1,
		},
		{
			name:     "valid path",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// override ProcessRmsData
			deviceProcessRmsData = func(header *pubsubdata.HeaderDetails, data []byte) (*helper.RmsStatusRecord, *helper.HeaderRecord, error) {
				if tc.processErr {
					return nil, nil, errors.New("process error")
				}
				return &helper.RmsStatusRecord{MonitorTime: time.Now().UTC(), Fault: "ok"}, &helper.HeaderRecord{Model: 1, FirmwareRevision: 1, FirmwareVersion: 1, CommVersion: 1}, nil
			}

			// setup redis mock
			rClient, rm := redismock.NewClientMock()
			tc.mockSetup(rm)

			// invoke
			out, err := getRedisDeviceStatus(context.Background(), rClient, tc.info, tc.gateways)

			// error assertion
			if tc.wantErr {
				if err == nil {
					t.Fatalf("%s: expected error, got none", tc.name)
				}
				return
			}
			if err != nil {
				t.Fatalf("%s: unexpected error: %v", tc.name, err)
			}

			// nil assertion
			if tc.wantNil {
				if out != nil {
					t.Errorf("%s: expected nil output, got %v", tc.name, out)
				}
				return
			}

			// content assertion
			if out == nil {
				t.Fatalf("%s: expected output, got nil", tc.name)
			}
			if len(*out) != tc.wantLen {
				t.Errorf("%s: len = %d; want %d", tc.name, len(*out), tc.wantLen)
			}
		})
	}
}
