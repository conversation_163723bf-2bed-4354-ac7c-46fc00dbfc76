package device

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"slices"
	"strconv"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

var (
	userInfoFromContext  = authorizer.UserInfoFromContext
	deviceProcessRmsData = devices.ProcessRmsData
)

func Handler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user info from jwt authorizer
	userInfo, ok := userInfoFromContext(ctx)
	if !ok {
		logger.Error("Unable to retrieve user info from request context")
		response.CreateInternalErrorResponse(w)
		return
	}

	allDevices, deviceId, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse API request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	pg := connections.Postgres
	rd := connections.Redis

	// Retrieve all device information and returns pointer
	// Only devices the user is authorized for will be returned.
	// Information includes: device info, device last fault info, and software gateway identifier.
	// Software gateway will later be used to know what redis key to pull for the latest data.
	deviceData, gatewayIds, err := getPgDeviceInfo(pg, userInfo, allDevices, deviceId)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Retrieve and append the current device status from redis to deviceData pointer
	data, err := getRedisDeviceStatus(ctx, rd, deviceData, gatewayIds)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(data, w)
}

var parseRequest = func(r *http.Request) (allDevices bool, deviceId int, err error) {
	rQuery := r.URL.Query()
	switch len(rQuery) {
	case 0:
		allDevices = true
	case 1:
		deviceIdString := rQuery.Get("deviceid")
		if deviceIdString == "" {
			return allDevices, deviceId, fmt.Errorf("%w: %v", ErrInvalidUrlQuery, rQuery)
		}
		deviceId, err = strconv.Atoi(deviceIdString)
		if err != nil {
			logger.Infof("unable to convert query string parameter deviceid to int: %v", err)
			return allDevices, deviceId, fmt.Errorf("%w: %v", ErrConvertQueryParam, err)
		}

		allDevices = false
	default:
		logger.Infof("unexpected usage - incorrect query string parameters")
		return allDevices, deviceId, ErrUnexpectedUsage
	}

	return allDevices, deviceId, nil
}

var getPgDeviceInfo = func(pg connect.DatabaseExecutor, userInfo *authorizer.UserInfo, allDevices bool, deviceId int) (*[]dataPayload, []string, error) {
	query := `SELECT
		COALESCE(df.MonitorTime, '1970-01-01 00:00:00'::timestamptz) AS MonitorTime,
		COALESCE(df.FaultStatus, '') AS FaultStatus,
		COALESCE(df.ChannelGreenStatus, '{}'::boolean[]) AS ChannelGreenStatus,
		COALESCE(df.ChannelYellowStatus, '{}'::boolean[]) AS ChannelYellowStatus,
		COALESCE(df.ChannelRedStatus, '{}'::boolean[]) AS ChannelRedStatus,
		d.Id,
		d.DeviceIdentifier,
		COALESCE(dcc.Latitude, '') AS Latitude,
		COALESCE(dcc.Longitude, '') AS Longitude,
		COALESCE(CASE WHEN ud.RoleId = 1 THEN dcc.IPAddress ELSE '***.***.***.***' END, '') as IPAddress,
		COALESCE(CASE WHEN ud.RoleId = 1 THEN dcc.Port ELSE '*****' END, '') as Port,
		COALESCE(dmn.MonitorId, 0) AS MonitorId,
		COALESCE(dmn.MonitorName, '') AS MonitorName,
		COALESCE(dre.EngineVersion, 0) AS EngineVersion,
		COALESCE(dre.EngineRevision, 0) AS EngineRevision,
		COALESCE(dl.DateUploadedUTC, '1970-01-01 00:00:00'::timestamptz) AS DateUploadedUTC,
		sg.SoftwareGatewayIdentifier
	FROM {{UserDevice}} as ud
	JOIN {{Device}} as d
		ON ud.DeviceId = d.Id
	JOIN {{SoftwareGateway}} as sg
		ON sg.Id = d.SoftwareGatewayId
	LEFT JOIN {{DeviceMonitorName}} as dmn
		ON dmn.DeviceIdentifier = d.DeviceIdentifier
	LEFT JOIN {{DeviceRMSEngine}} as dre
		ON dre.DeviceIdentifier = d.DeviceIdentifier
	LEFT JOIN {{DeviceCommunicationConfig}} as dcc
		ON dcc.DeviceIdentifier = d.DeviceIdentifier
	LEFT JOIN {{DeviceFault}} as df
		ON df.DeviceIdentifier = d.DeviceIdentifier
	LEFT JOIN {{DeviceLog}} as dl
		ON dl.DeviceIdentifier = d.DeviceIdentifier
	WHERE ud.UserId = $1`
	devices := &[]pgDeviceInfo{}
	if !allDevices {
		query = query + " AND ud.DeviceId = $2"
		err := pg.QueryGenericSlice(devices, query, userInfo.ID, deviceId)
		if err != nil {
			return nil, nil, err
		}
	} else {
		err := pg.QueryGenericSlice(devices, query, userInfo.ID)
		if err != nil {
			return nil, nil, err
		}
	}

	devs := *devices
	// get list of unique gateways
	gatewayIds := make([]string, 0, len(devs))
	for _, d := range devs {
		key := fmt.Sprintf("GatewayRMSData:%s", d.SoftwareGatewayIdentifier)
		// check against the few already collected
		if !slices.Contains(gatewayIds, key) {
			gatewayIds = append(gatewayIds, key)
		}
	}
	// Convert to dataPayload
	return convertPgDeviceInfos(devices), gatewayIds, nil
}

var getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, deviceInfo *[]dataPayload, gatewayIds []string) (*[]dataPayload, error) {
	if deviceInfo == nil || len(*deviceInfo) == 0 || len(gatewayIds) == 0 {
		return nil, nil
	}

	// build an index map for deviceId -> map for redis matching
	idxMap := make(map[string]int, len(*deviceInfo))
	for i, dp := range *deviceInfo {
		idxMap[dp.DeviceIdentifier] = i
	}

	vals, err := rd.MGet(ctx, gatewayIds...).Result()
	if err != nil {
		return nil, err
	}

	// pull rmsData for all gatewayIds
	for i, raw := range vals {
		// no data for gateway
		if raw == nil {
			logger.Warnf("no info for redis key: %s", gatewayIds[i])
			continue
		}
		// assert that it's a string
		str, ok := raw.(string)
		if !ok {
			logger.Errorf("unable to convert redis value to string: %s got type %v", gatewayIds[i], fmt.Sprintf("%T", raw))
			continue
		}
		var blob apiShared.RedisData
		err = json.Unmarshal([]byte(str), &blob)
		if err != nil {
			return nil, err
		}

		decode, err := base64.StdEncoding.DecodeString(blob.MsgData)
		if err != nil {
			logger.Errorf("%v: could not base64 decode redis message, %v", err, blob.MsgData)
			continue
		}
		msg := &gatewayv1.DeviceData{}
		if err = proto.Unmarshal(decode, msg); err != nil {
			logger.Errorf("%v: could not proto unmarshal redis message, %v", err, decode)
			continue
		}

		for i, d := range msg.GetMessages() {
			idx, found := idxMap[d.DeviceId]
			if !found {
				// non-requested device, continue to look for requested devices...
				continue
			}

			dp := &(*deviceInfo)[idx]

			logger.Debugf("/data/device endpoint, rms data from redis, processing record : %v, Deviceid : %v", i, d.DeviceId)
			pubsubheader := &pubsubdata.HeaderDetails{GatewayTimezone: blob.GatewayTimezone}
			rmsData, rmsHeader, err := deviceProcessRmsData(pubsubheader, d.GetMessage())
			if err != nil {
				// TODO: set status to error
				logger.Infof("Error parsing record: %v", err)
				continue
			}

			addRedisToPayload(dp, rmsHeader, rmsData)

		}

	}
	return deviceInfo, nil
}
