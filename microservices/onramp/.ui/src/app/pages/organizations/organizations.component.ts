import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GatewayService } from '../../services/gateway.service';

// TODO: Wire up backend once APIs are built
interface OEMData {
  orgId: string;
  apiKey: string;
  name: string;
}
@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  standalone: false,
  styleUrl: './organizations.component.css'
})
export class OrganizationsComponent {
  isModalVisible = false;
  isEditMode = false;
  selectedData = null;
  listOfData: OEMData[] = [];
  filteredList: OEMData[] = [];
  searchTerm: string = '';
  currentOEM: OEMData | null = null;
  editIndex: number | null = null;
  isTableLoading = false;
  organizationFilter: string | null = null;

  constructor(
    private gatewayService: GatewayService,
    private router: Router,
    private route: ActivatedRoute,
  ) { }
  ngOnInit() {
    this.getApi();
    this.route.queryParams.subscribe(params => {
      if (params) {
        this.searchTerm = params['organizationIdentifier'];
        this.organizationFilter = params['organizationIdentifier'] || null;
      }
    });
  }
  getApi() {
    this.isTableLoading = true;
    this.gatewayService.getOrganizations().subscribe((data) => {
      this.listOfData = data;
      this.filteredList = [...this.listOfData];
      this.isTableLoading = false;
      this.searchTable();
    },
      (error) => {
        console.error('Error fetching Gateway list:', error);
        this.isTableLoading = false;
      }
    );
  }
  searchTable(): void {
    this.filteredList = [...this.listOfData];
    if (this.searchTerm) {
      this.filteredList = this.listOfData.filter(item =>
        item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.orgId.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    if (this.organizationFilter) {
      this.filteredList = this.filteredList.filter(gateway =>
        gateway.orgId === this.organizationFilter,
      );
    }
  }
  onClickSoftware(value: any) {
    this.router.navigate(['/software-gateway'], {
      queryParams: { organizationIdentifier: value }
    });
  }
  onClickUsers() {
    console.log('click users');
  }
  openAddPopup() {
    this.isModalVisible = true;
    this.selectedData = null;
    this.isEditMode = false;
  }
  openEditPopup(index: number) {
    this.isModalVisible = true;
    this.isEditMode = true;
    this.editIndex = index;
    this.currentOEM = { ...this.listOfData[index] };
  }
  closeModal(): void {
    this.isModalVisible = false;
    this.selectedData = null;
    this.isEditMode = false;
  }
  handleModalSave(data: any): void {
    if (this.isEditMode && this.editIndex !== null && this.currentOEM) {
      const updatedOEM = { ...this.currentOEM, ...data };
      this.listOfData[this.editIndex] = updatedOEM;
    } else {
      this.listOfData = [...this.listOfData, data];
    }
    this.filteredList = [...this.listOfData];
    this.isModalVisible = false;
    this.editIndex = null;
    this.currentOEM = null;
  }
}
