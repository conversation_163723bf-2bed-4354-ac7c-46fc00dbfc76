// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProtectedComponent } from './protected/protected.component';
import { AuthGuard } from './guards/auth.guard';
import { HomeComponent } from './home/<USER>';
import { OrganizationsComponent } from './pages/organizations/organizations.component';
import { FooListComponent } from './foo-list/foo-list.component';
import { SoftwareGatewayComponent } from './pages/software-gateway/software-gateway.component';
import { SoftwareGatewayConfigurationComponent } from './pages/software-gateway-configuration/software-gateway-configuration.component';
import { DevicesComponent } from './pages/devices/devices.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'protected', component: ProtectedComponent, canActivate: [AuthGuard] },
  { path: 'organizations', component: OrganizationsComponent },
  { path: 'software-gateway', component: SoftwareGatewayComponent },
  { path: 'software-gateway-config', component: SoftwareGatewayConfigurationComponent },
  { path: 'devices', component: DevicesComponent },
  { path: 'foo', component: FooListComponent },
  { path: '**', redirectTo: 'home' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
