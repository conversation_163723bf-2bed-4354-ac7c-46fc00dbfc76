// main/router_test.go
package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks"
)

func TestNewRouter_Routes(t *testing.T) {
	// TODO: This will need to be revisited as our routes become more complex.
	router := NewRouter(mocks.FakeConns(), &mocks.FakeBatcher{})

	tests := []struct {
		name      string
		method    string
		path      string
		wantCode  int
		checkBody func(t *testing.T, rr *httptest.ResponseRecorder)
	}{
		{
			name:     "root static file (index.html)",
			method:   http.MethodGet,
			path:     "/__Do_Not_Add_Files_To_This_Directory.txt",
			wantCode: 200,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// We expect the static file to contain the warning text at the top.
				body := rr.Body.String()
				assert.True(t,
					strings.HasPrefix(body, "DO NOT ADD FILES TO THIS DIRECTORY"),
					"static index.txt content")
			},
		},
		{
			name:     "/foo handler",
			method:   http.MethodGet,
			path:     "/foo",
			wantCode: 200,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// 1. Content-Type must be JSON
				assert.Equal(t, "application/json", rr.Header().Get("Content-Type"), "Content-Type header")

				// 2. Unmarshal into []map[string]string and compare
				var got []map[string]string
				err := json.Unmarshal(rr.Body.Bytes(), &got)
				assert.NoError(t, err, "body should be valid JSON")

				want := []map[string]string{
					{"id": "001", "orgId": "org-001", "apiKey": "key-abc-123", "name": "OEM Alpha"},
					{"id": "002", "orgId": "org-002", "apiKey": "key-def-456", "name": "OEM Beta"},
					{"id": "003", "orgId": "org-003", "apiKey": "key-ghi-789", "name": "OEM Gamma"},
				}
				assert.Equal(t, want, got, "response JSON")
			},
		},
		{
			name:     "/api/foo handler",
			method:   http.MethodGet,
			path:     "/api/foo",
			wantCode: 200,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// 1. Content-Type must be JSON
				assert.Equal(t, "application/json", rr.Header().Get("Content-Type"), "Content-Type header")

				// 2. Unmarshal into []map[string]string and compare
				var got []map[string]string
				err := json.Unmarshal(rr.Body.Bytes(), &got)
				assert.NoError(t, err, "body should be valid JSON")

				want := []map[string]string{
					{"id": "001", "orgId": "org-001", "apiKey": "key-abc-123", "name": "OEM Alpha"},
					{"id": "002", "orgId": "org-002", "apiKey": "key-def-456", "name": "OEM Beta"},
					{"id": "003", "orgId": "org-003", "apiKey": "key-ghi-789", "name": "OEM Gamma"},
				}
				assert.Equal(t, want, got, "response JSON")
			},
		},
		{
			name:     "env.js asset",
			method:   http.MethodGet,
			path:     "/assets/env.js",
			wantCode: 200,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// We don’t need to inspect the body contents here—just confirm that it served 200 OK.
			},
		},
		{
			name:     "unknown path -> 404",
			method:   http.MethodGet,
			path:     "/not-a-route",
			wantCode: http.StatusNotFound,
			checkBody: func(t *testing.T, rr *httptest.ResponseRecorder) {
				// No further body inspection needed for a 404.
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest(tc.method, tc.path, nil)
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code, "status code for %s", tc.path)
			if tc.checkBody != nil {
				tc.checkBody(t, rr)
			}
		})
	}
}
