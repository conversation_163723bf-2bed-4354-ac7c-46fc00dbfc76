package handlers

import (
	"encoding/json"
	"net/http"
)

func Organizations(w http.ResponseWriter, r *http.Request) {
	gateways := []map[string]string{
		{"id": "001", "orgId": "org-001", "apiKey": "key-abc-123", "name": "OEM Alpha"},
		{"id": "002", "orgId": "org-002", "apiKey": "key-def-456", "name": "OEM Beta"},
		{"id": "003", "orgId": "org-003", "apiKey": "key-ghi-789", "name": "OEM Gamma"},
	}
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(gateways)
}
