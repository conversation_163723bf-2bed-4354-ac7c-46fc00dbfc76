package purgeExpired

import (
	"fmt"
	"time"

	"synapse-its.com/shared/connect"
)

const SOFTWARE_GATEWAY_INSTRUCTIONS_EXPIRATION_DAYS = 30

type PersistenceRepository interface {
	RemoveExpiredTokens() (int64, error)
	RemoveProcessedInstructions() (int64, error)
	GetAllDevicesAndKeys() (OrgDevSet, []string, error)
}

type repository struct {
	db connect.DatabaseExecutor
}

func (r *repository) RemoveExpiredTokens() (int64, error) {
	query := `
		DELETE FROM {{UserToken}}
		WHERE ExpirationUTC < $1
	`
	expirationTime := time.Now().UTC()
	results, err := r.db.Exec(query, expirationTime)
	if err != nil {
		return 0, fmt.Errorf("%w: %v", ErrRemoveExpiredTokens, err)
	}
	rowsAffected, err := results.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("%w: %v", ErrRemoveExpiredTokens, err)
	}
	return rowsAffected, nil
}

func (r *repository) RemoveProcessedInstructions() (int64, error) {
	query := `
		DELETE FROM {{SoftwareGatewayInstruction}} 
		WHERE DateReceivedUTC <= $1
	`
	expirationTime := time.Now().AddDate(0, 0, -SOFTWARE_GATEWAY_INSTRUCTIONS_EXPIRATION_DAYS)
	results, err := r.db.Exec(query, expirationTime)
	if err != nil {
		return 0, fmt.Errorf("%w: %v", ErrRemoveProcessedInstructions, err)
	}
	rowsAffected, err := results.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("%w: %v", ErrRemoveProcessedInstructions, err)
	}
	return rowsAffected, nil
}

func (r *repository) GetAllDevicesAndKeys() (OrgDevSet, []string, error) {
	query := `
		SELECT o.OrganizationIdentifier, sg.SoftwareGatewayIdentifier, d.DeviceIdentifier
		FROM {{SoftwareGateway}} sg
		INNER JOIN {{Device}} d ON sg.Id = d.SoftwareGatewayId 
		INNER JOIN {{Organization}} o ON sg.OrganizationId = o.Id 
	`
	results, err := r.db.QueryGeneric(query)
	if err != nil {
		return nil, nil, fmt.Errorf("error getting all devices: %w", err)
	}

	orgDevMap := make(OrgDevSet)
	gatewayKeyMap := make(map[string]string)

	for _, row := range results {
		orgId, ok1 := row["OrganizationIdentifier"].(string)
		deviceId, ok2 := row["DeviceIdentifier"].(string)
		sgwId, ok3 := row["SoftwareGatewayIdentifier"].(string)

		if ok1 && ok2 && ok3 {
			if orgDevMap[orgId] == nil {
				orgDevMap[orgId] = make(DeviceSet)
			}
			orgDevMap[orgId][deviceId] = nil
			gatewayKeyMap[sgwId] = fmt.Sprintf("%s:%s", redisKeyPrefix, sgwId)
		}
	}

	redisKeys := make([]string, 0, len(gatewayKeyMap))
	for _, value := range gatewayKeyMap {
		redisKeys = append(redisKeys, value)
	}

	return orgDevMap, redisKeys, nil
}
