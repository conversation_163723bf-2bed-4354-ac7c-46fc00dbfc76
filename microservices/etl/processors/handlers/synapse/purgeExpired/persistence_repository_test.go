package purgeExpired

import (
	"database/sql"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// fakeResult implements sql.Result for testing
type fakeResult struct {
	rowsAffected int64
	err          error
}

func (r *fakeResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (r *fakeResult) RowsAffected() (int64, error) {
	return r.rowsAffected, r.err
}

func TestPersistenceRepository_RemoveExpiredTokens(t *testing.T) {
	tests := []struct {
		name          string
		setupDB       func(*dbexecutor.FakeDBExecutor)
		expectedCount int64
		expectedError error
	}{
		{
			name: "successful removal",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{rowsAffected: 5}, nil
				}
			},
			expectedCount: 5,
			expectedError: nil,
		},
		{
			name: "database error",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			expectedCount: 0,
			expectedError: ErrRemoveExpiredTokens,
		},
		{
			name: "rows affected error",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{rowsAffected: 0, err: assert.AnError}, nil
				}
			},
			expectedCount: 0,
			expectedError: ErrRemoveExpiredTokens,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &dbexecutor.FakeDBExecutor{}
			tt.setupDB(db)

			repo := &repository{db: db}
			count, err := repo.RemoveExpiredTokens()

			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedCount, count)
		})
	}
}

func TestPersistenceRepository_RemoveProcessedInstructions(t *testing.T) {
	tests := []struct {
		name          string
		setupDB       func(*dbexecutor.FakeDBExecutor)
		expectedCount int64
		expectedError error
	}{
		{
			name: "successful removal",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{rowsAffected: 10}, nil
				}
			},
			expectedCount: 10,
			expectedError: nil,
		},
		{
			name: "database error",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			expectedCount: 0,
			expectedError: ErrRemoveProcessedInstructions,
		},
		{
			name: "rows affected error",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{rowsAffected: 0, err: assert.AnError}, nil
				}
			},
			expectedCount: 0,
			expectedError: ErrRemoveProcessedInstructions,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &dbexecutor.FakeDBExecutor{}
			tt.setupDB(db)

			repo := &repository{db: db}
			count, err := repo.RemoveProcessedInstructions()

			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedCount, count)
		})
	}
}

func TestPersistenceRepository_GetAllDevicesAndKeys(t *testing.T) {
	tests := []struct {
		name           string
		setupDB        func(*dbexecutor.FakeDBExecutor)
		expectedOrgDev OrgDevSet
		expectedKeys   []string
		expectedError  error
	}{
		{
			name: "successful retrieval",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
					return []map[string]interface{}{
						{
							"OrganizationIdentifier":    "org1",
							"SoftwareGatewayIdentifier": "sgw1",
							"DeviceIdentifier":          "dev1",
						},
						{
							"OrganizationIdentifier":    "org1",
							"SoftwareGatewayIdentifier": "sgw1",
							"DeviceIdentifier":          "dev2",
						},
						{
							"OrganizationIdentifier":    "org1",
							"SoftwareGatewayIdentifier": "sgw2",
							"DeviceIdentifier":          "dev3",
						},
						{
							"OrganizationIdentifier":    "org2",
							"SoftwareGatewayIdentifier": "sgw3",
							"DeviceIdentifier":          "dev4",
						},
					}, nil
				}
			},
			expectedOrgDev: OrgDevSet{
				"org1": DeviceSet{
					"dev1": nil,
					"dev2": nil,
					"dev3": nil,
				},
				"org2": DeviceSet{
					"dev4": nil,
				},
			},
			expectedKeys: []string{
				"GatewayRMSData:sgw1",
				"GatewayRMSData:sgw2",
				"GatewayRMSData:sgw3",
			},
			expectedError: nil,
		},
		{
			name: "database error",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
					return nil, assert.AnError
				}
			},
			expectedOrgDev: nil,
			expectedKeys:   nil,
			expectedError:  fmt.Errorf("error getting all devices: %w", assert.AnError),
		},
		{
			name: "empty result",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
					return []map[string]interface{}{}, nil
				}
			},
			expectedOrgDev: OrgDevSet{},
			expectedKeys:   []string{},
			expectedError:  nil,
		},
		{
			name: "invalid data types",
			setupDB: func(db *dbexecutor.FakeDBExecutor) {
				db.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
					return []map[string]interface{}{
						{
							"OrganizationIdentifier":    123, // invalid type
							"SoftwareGatewayIdentifier": "sgw1",
							"DeviceIdentifier":          "dev1",
						},
						{
							"OrganizationIdentifier":    "org1",
							"SoftwareGatewayIdentifier": 456, // invalid type
							"DeviceIdentifier":          "dev2",
						},
						{
							"OrganizationIdentifier":    "org1",
							"SoftwareGatewayIdentifier": "sgw2",
							"DeviceIdentifier":          789, // invalid type
						},
					}, nil
				}
			},
			expectedOrgDev: OrgDevSet{},
			expectedKeys:   []string{},
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &dbexecutor.FakeDBExecutor{}
			tt.setupDB(db)

			repo := &repository{db: db}
			result, keys, err := repo.GetAllDevicesAndKeys()

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			// Compare OrgDevSet
			assert.Equal(t, len(tt.expectedOrgDev), len(result))
			for orgID, expectedDevices := range tt.expectedOrgDev {
				resultDevices, exists := result[orgID]
				assert.True(t, exists, "Organization %s should exist in result", orgID)
				assert.Equal(t, len(expectedDevices), len(resultDevices))
				for deviceID := range expectedDevices {
					_, deviceExists := resultDevices[deviceID]
					assert.True(t, deviceExists, "Device %s should exist in organization %s", deviceID, orgID)
				}
			}

			// Compare Redis keys
			assert.Equal(t, len(tt.expectedKeys), len(keys))
			for _, expectedKey := range tt.expectedKeys {
				assert.Contains(t, keys, expectedKey)
			}
		})
	}
}
