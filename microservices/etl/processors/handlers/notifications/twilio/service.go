package twilio

import (
	"context"

	"github.com/twilio/twilio-go"
	twilioApi "github.com/twilio/twilio-go/rest/api/v2010"
	"synapse-its.com/shared/logger"
)

// TwilioClient defines the interface for Twilio API operations
type TwilioClient interface {
	CreateMessage(params *twilioApi.CreateMessageParams) (*twilioApi.ApiV2010Message, error)
}

// Service implements the NotificationService interface using Twilio
type Service struct {
	client TwilioClient // Injected Twilio client (optional)
}

// NewService creates a new Twilio notification service
func NewService() *Service {
	return &Service{}
}

// NewServiceWithClient creates a new Twilio notification service with injected client
func NewServiceWithClient(client TwilioClient) *Service {
	return &Service{
		client: client,
	}
}

// SendSMS sends an SMS message using Twilio
func (s *Service) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	// Get Twilio client (either injected or from context)
	client, err := s.getTwilioClient(ctx)
	if err != nil {
		logger.Error(err)
		return err
	}

	// Read TWILIO_FROM_PHONE from environment
	fromPhone := osGetenv("TWILIO_FROM_PHONE")
	if fromPhone == "" {
		logger.Error(ErrMissingFromPhone)
		return ErrMissingFromPhone
	}

	// Create message parameters
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(toPhone)
	params.SetFrom(fromPhone)
	params.SetBody(messageBody)

	// Send SMS via Twilio API
	_, err = client.CreateMessage(params)
	if err != nil {
		logger.Error(err)
		// Return Twilio errors so handler can decide between Ack/Nack
		return err
	}

	logger.Info("SMS sent successfully")
	return nil
}

// getTwilioClient returns the injected client or gets it from context
func (s *Service) getTwilioClient(ctx context.Context) (TwilioClient, error) {
	// Use injected client if available
	if s.client != nil {
		return s.client, nil
	}

	// Fallback to context-based client for backward compatibility
	contextClient := FromContext(ctx)
	if contextClient == nil {
		return nil, ErrClientNotFound
	}

	// Wrap the context client to implement our interface
	return &twilioClientAdapter{client: contextClient}, nil
}

// twilioClientAdapter adapts the context-based Twilio client to our interface
type twilioClientAdapter struct {
	client interface{}
}

func (a *twilioClientAdapter) CreateMessage(params *twilioApi.CreateMessageParams) (*twilioApi.ApiV2010Message, error) {
	// For backward compatibility with context-based client
	// Cast the client to the actual Twilio REST client
	if restClient, ok := a.client.(*twilio.RestClient); ok {
		return restClient.Api.CreateMessage(params)
	}
	return nil, ErrCreateMessage
}
