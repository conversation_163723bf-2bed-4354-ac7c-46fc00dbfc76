package faultNotification

import "time"

// A user eligible for notifications
type User struct {
	ID           int64  `json:"user_id"`
	Mobile       string `json:"mobile"`
	IANATimezone string `json:"iana_timezone"`
}

// FaultNotificationData represents the data for a fault notification
type FaultNotificationData struct {
	DeviceID       string    `json:"device_id"`
	UserDeviceID   string    `json:"user_device_id"`
	UserDeviceName string    `json:"user_device_name"`
	FaultReason    string    `json:"fault_reason"`
	FaultedAt      time.Time `json:"faulted_at"`
}

// NotificationRequest represents a notification request
// Same as the one in the NotificationMessage incoming
type NotificationRequest struct {
	Type     string                 `json:"type"`
	Payload  map[string]interface{} `json:"payload"`
	Metadata map[string]interface{} `json:"metadata"`
}
