package faultNotification

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	mockspubsub "synapse-its.com/shared/mocks/pubsub"
)

// Test_NewNotificationService tests the NewNotificationService constructor function
func Test_NewNotificationService(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userRepo       UserRepository
		publisher      NotificationPublisher
		websiteAppsURL string
		expectedType   string
	}{
		{
			name:           "success_with_valid_dependencies",
			userRepo:       &MockUserRepository{},
			publisher:      &MockNotificationPublisher{},
			websiteAppsURL: "https://example.com/apps/",
			expectedType:   "*faultNotification.notificationService",
		},
		{
			name:           "success_with_nil_dependencies",
			userRepo:       nil,
			publisher:      nil,
			websiteAppsURL: "",
			expectedType:   "*faultNotification.notificationService",
		},
		{
			name:           "success_with_empty_url",
			userRepo:       &MockUserRepository{},
			publisher:      &MockNotificationPublisher{},
			websiteAppsURL: "",
			expectedType:   "*faultNotification.notificationService",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := NewNotificationService(tt.userRepo, tt.publisher, tt.websiteAppsURL)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationService")
			assert.Implements(t, (*NotificationService)(nil), result, "should implement NotificationService interface")

			// Verify internal state
			service := result.(*notificationService)
			assert.Equal(t, tt.userRepo, service.userRepos, "should set userRepos correctly")
			assert.Equal(t, tt.publisher, service.publisher, "should set publisher correctly")
			assert.Equal(t, tt.websiteAppsURL, service.websiteAppsURL, "should set websiteAppsURL correctly")
		})
	}
}

// Test_NewNotificationPublisher tests the NewNotificationPublisher constructor function
func Test_NewNotificationPublisher(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		pubsubClient connect.PsClient
		topicName    string
		expectedType string
	}{
		{
			name:         "success_with_valid_client",
			pubsubClient: mockspubsub.NewFakePubsubClient(),
			topicName:    "test-topic",
			expectedType: "*faultNotification.notificationPublisher",
		},
		{
			name:         "success_with_nil_client",
			pubsubClient: nil,
			topicName:    "test-topic",
			expectedType: "*faultNotification.notificationPublisher",
		},
		{
			name:         "success_with_empty_topic",
			pubsubClient: mockspubsub.NewFakePubsubClient(),
			topicName:    "",
			expectedType: "*faultNotification.notificationPublisher",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := NewNotificationPublisher(tt.pubsubClient, tt.topicName)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationPublisher")
			assert.Implements(t, (*NotificationPublisher)(nil), result, "should implement NotificationPublisher interface")

			// Verify internal state
			publisher := result.(*notificationPublisher)
			assert.Equal(t, tt.pubsubClient, publisher.pubsubClient, "should set pubsubClient correctly")
			assert.Equal(t, tt.topicName, publisher.topicName, "should set topicName correctly")
		})
	}
}

// Test_ProcessFaultNotification tests the ProcessFaultNotification method
func Test_ProcessFaultNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name             string
		faultData        *FaultNotificationData
		setupUserRepo    func() UserRepository
		setupPublisher   func() NotificationPublisher
		websiteAppsURL   string
		expectedError    error
		expectedLogCount int
		wantErr          bool
	}{
		{
			name: "success_single_user_valid_mobile",
			faultData: &FaultNotificationData{
				DeviceID:       "device-123",
				UserDeviceID:   "device-123",
				UserDeviceName: "Test Device",
				FaultReason:    "High Temperature",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{
						{
							ID:           1,
							Mobile:       "1234567890",
							IANATimezone: "America/New_York",
						},
					}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "success_multiple_users",
			faultData: &FaultNotificationData{
				DeviceID:       "device-456",
				UserDeviceID:   "device-456",
				UserDeviceName: "Multi Device",
				FaultReason:    "Low Voltage",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{
						{ID: 1, Mobile: "1234567890", IANATimezone: "America/New_York"},
						{ID: 2, Mobile: "0987654321", IANATimezone: "America/Los_Angeles"},
					}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "error_user_lookup_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-error",
				UserDeviceID:   "device-error",
				UserDeviceName: "Error Device",
				FaultReason:    "Connection Lost",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return nil, errors.New("database connection failed")
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrUserLookup,
			wantErr:        true,
		},
		{
			name: "success_no_users_found",
			faultData: &FaultNotificationData{
				DeviceID:       "device-no-users",
				UserDeviceID:   "device-no-users",
				UserDeviceName: "No Users Device",
				FaultReason:    "System Error",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
		{
			name: "error_invalid_mobile_phone",
			faultData: &FaultNotificationData{
				DeviceID:       "device-invalid-mobile",
				UserDeviceID:   "device-invalid-mobile",
				UserDeviceName: "Invalid Mobile Device",
				FaultReason:    "Test Error",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{
						{ID: 1, Mobile: "123", IANATimezone: "America/New_York"}, // Invalid mobile - too short
					}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				return &MockNotificationPublisher{}
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrSendAllNotificationsFailed,
			wantErr:        true,
		},
		{
			name: "error_publish_notification_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-publish-error",
				UserDeviceID:   "device-publish-error",
				UserDeviceName: "Publish Error Device",
				FaultReason:    "Publish Test",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{
						{ID: 1, Mobile: "1234567890", IANATimezone: "America/New_York"},
					}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *NotificationRequest) error {
					return errors.New("publish failed")
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  ErrSendAllNotificationsFailed,
			wantErr:        true,
		},
		{
			name: "partial_success_some_notifications_failed",
			faultData: &FaultNotificationData{
				DeviceID:       "device-partial",
				UserDeviceID:   "device-partial",
				UserDeviceName: "Partial Device",
				FaultReason:    "Partial Test",
				FaultedAt:      time.Now().UTC(),
			},
			setupUserRepo: func() UserRepository {
				repo := &MockUserRepository{}
				repo.GetEligibleUsersFunc = func(ctx context.Context, deviceID string) ([]User, error) {
					return []User{
						{ID: 1, Mobile: "1234567890", IANATimezone: "America/New_York"},
						{ID: 2, Mobile: "123", IANATimezone: "America/Los_Angeles"}, // Invalid mobile
					}, nil
				}
				return repo
			},
			setupPublisher: func() NotificationPublisher {
				pub := &MockNotificationPublisher{}
				pub.PublishNotificationFunc = func(ctx context.Context, notification *NotificationRequest) error {
					return nil
				}
				return pub
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedError:  nil,
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup service
			userRepo := tt.setupUserRepo()
			publisher := tt.setupPublisher()
			service := NewNotificationService(userRepo, publisher, tt.websiteAppsURL)

			// Execute function under test
			ctx := context.Background()
			err := service.ProcessFaultNotification(ctx, tt.faultData)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got nil")
				if tt.expectedError != nil {
					assert.Equal(t, tt.expectedError, err, "should return expected error")
				}
			} else {
				assert.NoError(t, err, "unexpected error")
			}
		})
	}
}

// Test_constructNotification tests the constructNotification method
func Test_constructNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name             string
		faultData        *FaultNotificationData
		user             *User
		websiteAppsURL   string
		expectedType     string
		expectedPayload  map[string]interface{}
		expectedMetadata map[string]interface{}
	}{
		{
			name: "success_normal_case",
			faultData: &FaultNotificationData{
				DeviceID:       "device-123",
				UserDeviceID:   "user-device-123",
				UserDeviceName: "Test Device Name",
				FaultReason:    "High Temperature Alert",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &User{
				ID:           42,
				Mobile:       "1234567890",
				IANATimezone: "America/New_York",
			},
			websiteAppsURL: "https://example.com/apps/",
			expectedType:   "sms",
			expectedPayload: map[string]interface{}{
				"to":      "1234567890",
				"message": "EDIFSA\n\nDevice:\nID: user-device-123\nName: Test Device Name\n\nMsg:\nHigh Temperature Alert\n\nDetail:\nhttps://example.com/apps/device-123",
			},
			expectedMetadata: map[string]interface{}{
				"device_id":        "device-123",
				"user_device_id":   "user-device-123",
				"user_device_name": "Test Device Name",
				"fault_reason":     "High Temperature Alert",
				"faulted_at":       time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
				"user_id":          int64(42),
				"user_timezone":    "America/New_York",
			},
		},
		{
			name: "success_long_device_name_truncated",
			faultData: &FaultNotificationData{
				DeviceID:       "device-456",
				UserDeviceID:   "user-device-456",
				UserDeviceName: "This is a very long device name that should be truncated",
				FaultReason:    "Error",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &User{
				ID:           1,
				Mobile:       "0987654321",
				IANATimezone: "UTC",
			},
			websiteAppsURL: "https://test.com/",
			expectedType:   "sms",
			expectedPayload: map[string]interface{}{
				"to":      "0987654321",
				"message": "EDIFSA\n\nDevice:\nID: user-device-456\nName: This is a very long devic...\n\nMsg:\nError\n\nDetail:\nhttps://test.com/device-456",
			},
			expectedMetadata: map[string]interface{}{
				"device_id":        "device-456",
				"user_device_id":   "user-device-456",
				"user_device_name": "This is a very long device name that should be truncated",
				"fault_reason":     "Error",
				"faulted_at":       time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
				"user_id":          int64(1),
				"user_timezone":    "UTC",
			},
		},
		{
			name: "success_long_fault_reason_truncated",
			faultData: &FaultNotificationData{
				DeviceID:       "device-789",
				UserDeviceID:   "user-device-789",
				UserDeviceName: "Device",
				FaultReason:    "This is a very long fault reason that should be truncated properly",
				FaultedAt:      time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
			},
			user: &User{
				ID:           99,
				Mobile:       "5555555555",
				IANATimezone: "Europe/London",
			},
			websiteAppsURL: "https://app.example.com/",
			expectedType:   "sms",
			expectedPayload: map[string]interface{}{
				"to":      "5555555555",
				"message": "EDIFSA\n\nDevice:\nID: user-device-789\nName: Device\n\nMsg:\nThis is a very long fau...\n\nDetail:\nhttps://app.example.com/device-789",
			},
			expectedMetadata: map[string]interface{}{
				"device_id":        "device-789",
				"user_device_id":   "user-device-789",
				"user_device_name": "Device",
				"fault_reason":     "This is a very long fault reason that should be truncated properly",
				"faulted_at":       time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC),
				"user_id":          int64(99),
				"user_timezone":    "Europe/London",
			},
		},
		{
			name: "success_empty_strings",
			faultData: &FaultNotificationData{
				DeviceID:       "",
				UserDeviceID:   "",
				UserDeviceName: "",
				FaultReason:    "",
				FaultedAt:      time.Time{},
			},
			user: &User{
				ID:           0,
				Mobile:       "",
				IANATimezone: "",
			},
			websiteAppsURL: "",
			expectedType:   "sms",
			expectedPayload: map[string]interface{}{
				"to":      "",
				"message": "EDIFSA\n\nDevice:\nID: \nName: \n\nMsg:\n\n\nDetail:\n",
			},
			expectedMetadata: map[string]interface{}{
				"device_id":        "",
				"user_device_id":   "",
				"user_device_name": "",
				"fault_reason":     "",
				"faulted_at":       time.Time{},
				"user_id":          int64(0),
				"user_timezone":    "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup service
			service := &notificationService{
				websiteAppsURL: tt.websiteAppsURL,
			}

			// Execute function under test
			result := service.constructNotification(tt.faultData, tt.user)

			// Assert results
			assert.NotNil(t, result, "should return non-nil NotificationRequest")
			assert.Equal(t, tt.expectedType, result.Type, "should set correct notification type")
			assert.Equal(t, tt.expectedPayload, result.Payload, "should construct correct payload")
			assert.Equal(t, tt.expectedMetadata, result.Metadata, "should set correct metadata")
		})
	}
}

// Test_PublishNotification tests the PublishNotification method
func Test_PublishNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		notification  *NotificationRequest
		setupPubsub   func() connect.PsClient
		topicName     string
		expectedError error
		wantErr       bool
	}{
		{
			name: "success_valid_notification",
			notification: &NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"to":      "1234567890",
					"message": "Test message",
				},
				Metadata: map[string]interface{}{
					"device_id": "device-123",
				},
			},
			setupPubsub: func() connect.PsClient {
				return mocks.FakeConns().Pubsub
			},
			topicName:     "test-topic",
			expectedError: nil,
			wantErr:       false,
		},
		{
			name: "error_marshal_json_failed",
			notification: &NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"invalid": make(chan int), // Channels cannot be marshaled to JSON
				},
			},
			setupPubsub: func() connect.PsClient {
				return mocks.FakeConns().Pubsub
			},
			topicName:     "test-topic",
			expectedError: ErrMarshalJSONNotification,
			wantErr:       true,
		},
		{
			name: "error_publish_failed",
			notification: &NotificationRequest{
				Type: "sms",
				Payload: map[string]interface{}{
					"to":      "1234567890",
					"message": "Test message",
				},
			},
			setupPubsub: func() connect.PsClient {
				fakePubsub := mockspubsub.NewFakePubsubClient()
				fakePubsub.PublishError = errors.New("pubsub publish error")
				return fakePubsub
			},
			topicName:     "test-topic",
			expectedError: ErrNotificationPublish,
			wantErr:       true,
		},
		{
			name: "success_empty_notification",
			notification: &NotificationRequest{
				Type:     "",
				Payload:  map[string]interface{}{},
				Metadata: map[string]interface{}{},
			},
			setupPubsub: func() connect.PsClient {
				return mocks.FakeConns().Pubsub
			},
			topicName:     "empty-topic",
			expectedError: nil,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup publisher
			pubsubClient := tt.setupPubsub()
			publisher := NewNotificationPublisher(pubsubClient, tt.topicName)

			// Execute function under test
			ctx := context.Background()
			err := publisher.PublishNotification(ctx, tt.notification)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got nil")
				if tt.expectedError != nil {
					assert.Equal(t, tt.expectedError, err, "should return expected error")
				}
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify message was published correctly for success cases
			if !tt.wantErr {
				fakePubsub := pubsubClient.(*mockspubsub.FakePubsubClient)
				topic := fakePubsub.Topic(tt.topicName)
				fakeTopic := topic.(*mockspubsub.FakePubsubTopic)

				publishedMessages := fakeTopic.GetMessages()
				assert.True(t, len(publishedMessages) > 0, "should have published at least one message")

				if len(publishedMessages) > 0 {
					msg := publishedMessages[0]

					// Verify message data can be unmarshaled
					var notification NotificationRequest
					err := json.Unmarshal(msg.Data, &notification)
					assert.NoError(t, err, "published message should be valid JSON")

					// Verify attributes
					assert.Equal(t, tt.topicName, msg.Attributes["topic"], "should set topic attribute correctly")
				}
			}
		})
	}
}

// Test_shortenString tests the shortenString utility function
func Test_shortenString(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		input    string
		maxChars int
		expected string
	}{
		{
			name:     "success_string_within_limit",
			input:    "Short string",
			maxChars: 20,
			expected: "Short string",
		},
		{
			name:     "success_string_exactly_at_limit",
			input:    "Exactly twenty chars",
			maxChars: 20,
			expected: "Exactly twenty chars",
		},
		{
			name:     "success_string_exceeds_limit",
			input:    "This is a very long string that exceeds the character limit",
			maxChars: 20,
			expected: "This is a very long ...",
		},
		{
			name:     "success_empty_string",
			input:    "",
			maxChars: 10,
			expected: "",
		},
		{
			name:     "success_single_character",
			input:    "A",
			maxChars: 1,
			expected: "A",
		},
		{
			name:     "success_zero_max_chars",
			input:    "Any string",
			maxChars: 0,
			expected: "...",
		},
		{
			name:     "success_unicode_characters",
			input:    "Hello 世界! This is a test with unicode characters",
			maxChars: 15,
			expected: "Hello 世界! This ...",
		},
		{
			name:     "success_emoji_characters",
			input:    "Test with emojis 😀😃😄😁😆",
			maxChars: 10,
			expected: "Test with ...",
		},
		{
			name:     "success_negative_max_chars",
			input:    "Any string",
			maxChars: -5,
			expected: "...",
		},
		{
			name:     "success_very_large_max_chars",
			input:    "Short",
			maxChars: 1000,
			expected: "Short",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := shortenString(tt.input, tt.maxChars)

			// Assert results
			assert.Equal(t, tt.expected, result, "should return expected shortened string")

			// Additional validation for proper behavior
			if tt.maxChars > 0 && len(tt.input) <= tt.maxChars {
				assert.Equal(t, tt.input, result, "should return original string when within limit")
				assert.NotContains(t, result, "...", "should not contain ellipsis when within limit")
			}

			if tt.maxChars > 0 && len(tt.input) > tt.maxChars {
				assert.Contains(t, result, "...", "should contain ellipsis when truncated")
			}
		})
	}
}

// Test interface compliance
func Test_notificationService_interface_compliance(t *testing.T) {
	t.Parallel()

	t.Run("implements_NotificationService_interface", func(t *testing.T) {
		t.Parallel()

		// Setup
		service := &notificationService{}

		// Assert interface compliance
		assert.Implements(t, (*NotificationService)(nil), service, "notificationService should implement NotificationService interface")
	})
}

func Test_notificationPublisher_interface_compliance(t *testing.T) {
	t.Parallel()

	t.Run("implements_NotificationPublisher_interface", func(t *testing.T) {
		t.Parallel()

		// Setup
		publisher := &notificationPublisher{}

		// Assert interface compliance
		assert.Implements(t, (*NotificationPublisher)(nil), publisher, "notificationPublisher should implement NotificationPublisher interface")
	})
}

// Mock implementations for testing
type MockUserRepository struct {
	GetEligibleUsersFunc      func(ctx context.Context, deviceID string) ([]User, error)
	GetEligibleUsersCallCount int
}

func (m *MockUserRepository) GetEligibleUsers(ctx context.Context, deviceID string) ([]User, error) {
	m.GetEligibleUsersCallCount++
	if m.GetEligibleUsersFunc != nil {
		return m.GetEligibleUsersFunc(ctx, deviceID)
	}
	return []User{}, nil
}

type MockNotificationPublisher struct {
	PublishNotificationFunc      func(ctx context.Context, notification *NotificationRequest) error
	PublishNotificationCallCount int
}

func (m *MockNotificationPublisher) PublishNotification(ctx context.Context, notification *NotificationRequest) error {
	m.PublishNotificationCallCount++
	if m.PublishNotificationFunc != nil {
		return m.PublishNotificationFunc(ctx, notification)
	}
	return nil
}
