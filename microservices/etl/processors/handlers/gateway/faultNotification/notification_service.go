package faultNotification

import (
	"context"
	"encoding/json"
	"fmt"
	"unicode/utf8"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// NotificationService defines the interface for processing fault notifications
type NotificationService interface {
	ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error
}

// NotificationPublisher defines the interface for publishing notifications
type NotificationPublisher interface {
	PublishNotification(ctx context.Context, notification *NotificationRequest) error
}

// notificationService implements NotificationService interface
type notificationService struct {
	userRepos      UserRepository
	publisher      NotificationPublisher
	websiteAppsURL string
}

// NewNotificationService creates a new NotificationService instance
func NewNotificationService(userRepo UserRepository, publisher NotificationPublisher, websiteAppsURL string) NotificationService {
	return &notificationService{
		userRepos:      userRep<PERSON>,
		publisher:      publisher,
		websiteAppsURL: websiteAppsURL,
	}
}

// notificationPublisher implements NotificationPublisher interface
type notificationPublisher struct {
	pubsubClient connect.PsClient
	topicName    string
}

func NewNotificationPublisher(pubsubClient connect.PsClient, topicName string) NotificationPublisher {
	return &notificationPublisher{
		pubsubClient: pubsubClient,
		topicName:    topicName,
	}
}

// ProcessFaultNotification processes a fault notification
func (s *notificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
	// Get eligible users for the device
	users, err := s.userRepos.GetEligibleUsers(ctx, faultData.DeviceID)
	if err != nil {
		logger.Errorf("Failed to get eligible users for device %s: %v", faultData.DeviceID, err)
		return ErrUserLookup
	}

	if len(users) == 0 {
		logger.Infof("No eligible users found for device %s", faultData.DeviceID)
		return nil // No notifications to send
	}

	logger.Infof("Found %d eligible users for device %s", len(users), faultData.DeviceID)

	// Process notifications for each user
	successCount := 0
	for _, user := range users {
		if len(user.Mobile) < 10 {
			logger.Error(ErrInvalidMobilePhone)
			continue
		}

		// Construct notification for this user
		notification := s.constructNotification(faultData, &user)

		// Publish notification
		err := s.publisher.PublishNotification(ctx, notification)
		if err != nil {
			logger.Errorf("Failed to publish notification for user %d, device %s: %v", user.ID, faultData.DeviceID, err)
			continue
		}

		logger.Debugf("Successfully published notification for user %d, device %s", user.ID, faultData.DeviceID)
		successCount++
	}

	// Check result
	if successCount == 0 {
		logger.Errorf("Failed to send any notifications for device %s", faultData.DeviceID)
		return ErrSendAllNotificationsFailed
	}

	if successCount < len(users) {
		logger.Warnf("Sent %d/%d notifications for device %s", successCount, len(users), faultData.DeviceID)
	} else {
		logger.Infof("Successfully sent all %d notifications for device %s", successCount, faultData.DeviceID)
	}

	return nil
}

// constructNotification creates a notification request for a specific user
func (s *notificationService) constructNotification(faultData *FaultNotificationData, user *User) *NotificationRequest {
	message := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s",
		faultData.UserDeviceID,
		shortenString(faultData.UserDeviceName, 25),
		shortenString(faultData.FaultReason, 23),
		s.websiteAppsURL+faultData.DeviceID,
	)

	return &NotificationRequest{
		Type: "sms",
		Payload: map[string]interface{}{
			"to":      user.Mobile,
			"message": message,
		},
		Metadata: map[string]interface{}{
			"device_id":        faultData.DeviceID,
			"user_device_id":   faultData.UserDeviceID,
			"user_device_name": faultData.UserDeviceName,
			"fault_reason":     faultData.FaultReason,
			"faulted_at":       faultData.FaultedAt,
			"user_id":          user.ID,
			"user_timezone":    user.IANATimezone,
		},
	}
}

// PublishNotification publishes a notification to the notification-alerts-topic
func (p *notificationPublisher) PublishNotification(ctx context.Context, notification *NotificationRequest) error {
	logger.Debugf("Publishing notification to topic: %s", p.topicName)

	// Marshal notification to JSON
	data, err := json.Marshal(notification)
	if err != nil {
		logger.Errorf("Failed to marshal notification: %v", err)
		return ErrMarshalJSONNotification
	}

	// Publish to notification-alerts-topic
	topic := p.pubsubClient.Topic(p.topicName)
	result := topic.Publish(ctx, &pubsub.Message{
		Data: data,
		Attributes: map[string]string{
			"topic": p.topicName,
		},
	})

	// Wait for publish result
	_, err = result.Get(ctx)
	if err != nil {
		logger.Errorf("Failed to publish notification: %v", err)
		return ErrNotificationPublish
	}

	logger.Debugf("Successfully published notification to topic: %s", p.topicName)
	return nil
}

// shortenString truncates a string to maxChars length, preserving UTF-8 encoding
func shortenString(input string, maxChars int) string {
	// Handle edge cases for zero or negative maxChars
	if maxChars <= 0 {
		return "..."
	}

	// Return the input string if it's already within the limit
	runeCount := utf8.RuneCountInString(input)
	if runeCount <= maxChars {
		return input
	}

	// Truncate to maxChars characters and add "..."
	runes := []rune(input)
	shortened := string(runes[:maxChars])
	return shortened + "..."
}
