package faultNotification

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// Test_NewUserRepository tests the constructor function
func Test_NewUserRepository(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		dbMock   connect.DatabaseExecutor
		expected UserRepository
	}{
		{
			name:     "success_with_valid_db_executor",
			dbMock:   &mocks.FakeDBExecutor{},
			expected: &userRepository{db: &mocks.FakeDBExecutor{}},
		},
		{
			name:     "success_with_nil_db_executor",
			dbMock:   nil,
			expected: &userRepository{db: nil},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function under test
			result := NewUserRepository(tt.dbMock)

			// Assert results
			assert.NotNil(t, result, "NewUserRepository should return non-nil UserRepository")
			assert.IsType(t, &userRepository{}, result, "should return userRepository type")

			// Cast to concrete type to check db field
			concrete, ok := result.(*userRepository)
			assert.True(t, ok, "should be able to cast to concrete type")
			assert.Equal(t, tt.dbMock, concrete.db, "database executor should be set correctly")
		})
	}
}

// Test_GetEligibleUsers tests the GetEligibleUsers method
func Test_GetEligibleUsers(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		deviceID           string
		setupMockFn        func() *mocks.FakeDBExecutor
		expectedUsers      []User
		expectedErr        error
		wantErr            bool
		expectedQueryCalls int
	}{
		{
			name:     "success_with_users_found",
			deviceID: "device-123",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query with users found
					users := dest.(*[]User)
					*users = []User{
						{ID: 1, Mobile: "+1234567890", IANATimezone: "America/New_York"},
						{ID: 2, Mobile: "+0987654321", IANATimezone: "America/Los_Angeles"},
					}
					return nil
				}
				return mockDB
			},
			expectedUsers: []User{
				{ID: 1, Mobile: "+1234567890", IANATimezone: "America/New_York"},
				{ID: 2, Mobile: "+0987654321", IANATimezone: "America/Los_Angeles"},
			},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:     "success_with_no_users_found",
			deviceID: "device-456",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query but no users found
					users := dest.(*[]User)
					*users = []User{} // Empty slice
					return nil
				}
				return mockDB
			},
			expectedUsers:      []User{},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:     "database_error",
			deviceID: "device-789",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
				return mockDB
			},
			expectedUsers:      nil,
			expectedErr:        ErrUserLookup,
			wantErr:            true,
			expectedQueryCalls: 1,
		},
		{
			name:     "empty_device_id",
			deviceID: "",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query with empty device ID
					users := dest.(*[]User)
					*users = []User{}
					return nil
				}
				return mockDB
			},
			expectedUsers:      []User{},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:     "single_user_found",
			deviceID: "device-single",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query with single user
					users := dest.(*[]User)
					*users = []User{
						{ID: 42, Mobile: "+5551234567", IANATimezone: "UTC"},
					}
					return nil
				}
				return mockDB
			},
			expectedUsers: []User{
				{ID: 42, Mobile: "+5551234567", IANATimezone: "UTC"},
			},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:     "query_generic_slice_call_limit_error",
			deviceID: "device-limit",
			setupMockFn: func() *mocks.FakeDBExecutor {
				mockDB := &mocks.FakeDBExecutor{}
				mockDB.EnableFailAfter = true
				mockDB.QueryGenericSliceCallFailAfter = 0
				return mockDB
			},
			expectedUsers:      nil,
			expectedErr:        ErrUserLookup,
			wantErr:            true,
			expectedQueryCalls: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := tt.setupMockFn()

			// Create repository instance
			repo := NewUserRepository(mockDB)

			// Execute function under test
			ctx := context.Background()
			result, err := repo.GetEligibleUsers(ctx, tt.deviceID)

			// Assert error conditions
			if tt.wantErr {
				assert.Error(t, err, "should return error")
				assert.ErrorIs(t, err, tt.expectedErr, "should return expected error type")
				assert.Nil(t, result, "should return nil users on error")
			} else {
				assert.NoError(t, err, "should not return error")
				assert.Equal(t, tt.expectedUsers, result, "should return expected users")
			}

			// Assert database interaction
			assert.Equal(t, tt.expectedQueryCalls, mockDB.QueryGenericSliceCallCount, "should call QueryGenericSlice expected number of times")

			// Verify query parameters were passed correctly (if successful)
			if !tt.wantErr && mockDB.QueryGenericSliceFunc != nil {
				// The query and parameters were validated indirectly through the mock setup
			}
		})
	}
}

// Test_GetEligibleUsers_query_validation tests query construction
func Test_GetEligibleUsers_query_validation(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		deviceID      string
		expectedQuery string
		expectedArgs  []interface{}
	}{
		{
			name:     "query_structure_validation",
			deviceID: "test-device-123",
			expectedQuery: `
		SELECT u.Id, u.Mobile, u.IANATimezone
		FROM {{User}} u
		INNER JOIN {{UserDevice}} ud ON u.Id = ud.UserId
		WHERE ud.DeviceId = $1
			AND u.Mobile IS NOT NULL
			AND u.IsEnabled = 1
			AND u.NotificationSmsEnabled = true
			AND u.IsDeleted = false
			AND ud.IsDeleted = false`,
			expectedArgs: []interface{}{"test-device-123"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock to capture query and args
			var capturedQuery string
			var capturedArgs []interface{}

			mockDB := &mocks.FakeDBExecutor{}
			mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				capturedQuery = query
				capturedArgs = args

				// Return empty users to complete the test
				users := dest.(*[]User)
				*users = []User{}
				return nil
			}

			// Create repository and execute
			repo := NewUserRepository(mockDB)
			ctx := context.Background()
			_, err := repo.GetEligibleUsers(ctx, tt.deviceID)

			// Assert no error
			assert.NoError(t, err, "should not return error")

			// Assert query structure contains expected elements
			assert.Contains(t, capturedQuery, "SELECT u.Id, u.Mobile, u.IANATimezone", "query should select correct fields")
			assert.Contains(t, capturedQuery, "FROM {{User}} u", "query should use User table")
			assert.Contains(t, capturedQuery, "INNER JOIN {{UserDevice}} ud", "query should join UserDevice table")
			assert.Contains(t, capturedQuery, "WHERE ud.DeviceId = $1", "query should filter by device ID")
			assert.Contains(t, capturedQuery, "u.Mobile IS NOT NULL", "query should filter non-null mobile")
			assert.Contains(t, capturedQuery, "u.IsEnabled = 1", "query should filter enabled users")
			assert.Contains(t, capturedQuery, "u.NotificationSmsEnabled = true", "query should filter SMS enabled users")
			assert.Contains(t, capturedQuery, "u.IsDeleted = false", "query should filter non-deleted users")
			assert.Contains(t, capturedQuery, "ud.IsDeleted = false", "query should filter non-deleted user devices")

			// Assert arguments
			assert.Equal(t, tt.expectedArgs, capturedArgs, "should pass correct arguments")
		})
	}
}

// Test_userRepository_interface_compliance tests that userRepository implements UserRepository interface
func Test_userRepository_interface_compliance(t *testing.T) {
	t.Parallel()

	t.Run("implements_UserRepository_interface", func(t *testing.T) {
		// Create instance
		mockDB := &mocks.FakeDBExecutor{}
		repo := NewUserRepository(mockDB)

		// Assert interface compliance
		var _ UserRepository = repo
		assert.Implements(t, (*UserRepository)(nil), repo, "userRepository should implement UserRepository interface")
	})
}
