package faultNotification

import (
	"context"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// UserRepository defines the interface for retrieving eligible users
type UserRepository interface {
	GetEligibleUsers(ctx context.Context, deviceID string) ([]User, error)
}

// userRepository implements UserRepository interface
type userRepository struct {
	db connect.DatabaseExecutor
}

// NewUserRepository creates a new UserRepository instance
func NewUserRepository(db connect.DatabaseExecutor) UserRepository {
	return &userRepository{
		db: db,
	}
}

// GetEligibleUsers returns all eligible users for the given device
func (r *userRepository) GetEligibleUsers(ctx context.Context, deviceID string) ([]User, error) {
	query := `
		SELECT u.Id, u.Mobile, u.IANATimezone
		FROM {{User}} u
		INNER JOIN {{UserDevice}} ud ON u.Id = ud.UserId
		WHERE ud.DeviceId = $1
			AND u.Mobile IS NOT NULL
			AND u.IsEnabled = 1
			AND u.NotificationSmsEnabled = true
			AND u.IsDeleted = false
			AND ud.IsDeleted = false`

	logger.Debugf("Querying eligible users for device: %s", deviceID)

	var users []User
	err := r.db.QueryGenericSlice(&users, query, deviceID)
	if err != nil {
		logger.Errorf("Failed to query eligible users for device %s: %v", deviceID, err)
		return nil, ErrUserLookup
	}

	logger.Infof("Found %d eligible users for device %s", len(users), deviceID)
	return users, nil
}
