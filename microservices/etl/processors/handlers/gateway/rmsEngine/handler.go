package rmsEngine

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ProcessRMSEngineFunc    func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsEngineRecord *edihelper.RmsEngineRecord, headerDetails *edihelper.HeaderRecord, err error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, header schemas.HeaderRecord, rawMsg []byte, engineData *edihelper.RmsEngineRecord) schemas.RmsEngine
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
	UpsertFunc              func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceRmsEngine) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector        ConnectorFunc
	ParseAttributes  ParseAttributesFunc
	SendToDLQ        DLQSender
	UnmarshalDevice  UnmarshalDeviceDataFunc
	GetBatch         BatchGetter
	ProcessRMSEngine ProcessRMSEngineFunc
	ToBQ             ToBQConverter
	MarshalDevice    MarshalDeviceDataFunc
	UpsertDevice     UpsertFunc
}

// HandlerWithDeps constructs a Pub/Sub processing function with injected dependencies.
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		// Acquire connections
		conns, err := deps.Connector(ctx)
		pg := conns.Postgres
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}

		sub := conns.Pubsub.Subscription(subscriptionName)

		// Set handler
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Data: %s", subscriptionName, msg.ID, string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Protobuf body
			dd, errUm := deps.UnmarshalDevice(msg.Data)
			if errUm != nil {
				logger.Errorf("Error unmarshaling device data: %v", errUm)
				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Collect failed messages
			unprocessed := new(gatewayv1.DeviceData)
			validRecords := make(map[string]*edihelper.DeviceRmsEngine)
			for i, d := range dd.GetMessages() {
				logger.Debugf("Processing record %d: DeviceId=%v", i, d.DeviceId)
				rmsEngineData, rmsHdr, perr := deps.ProcessRMSEngine(&httpHeader, d.GetMessage())
				if perr != nil {
					logger.Infof("Error parsing record: %v", perr)
					unprocessed.Messages = append(unprocessed.Messages, d)
					continue
				}

				item := deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					*rmsHdr.ToBigQuerySchema(),
					d.GetMessage(),
					rmsEngineData,
				)
				if err = batch.Add(item); err != nil {
					logger.Infof("Error adding to batch: %v", err)
					unprocessed.Messages = append(unprocessed.Messages, d)
				}

				validRecords[d.DeviceId] = &edihelper.DeviceRmsEngine{
					DeviceIdentifier: d.DeviceId,
					EngineVersion:    rmsEngineData.EngineVersion,
					EngineRevision:   rmsEngineData.EngineRevision,
					PubsubTimestamp:  msg.PublishTime.UTC(),
					UpdatedAt:        time.Now(),
				}
			}

			if len(validRecords) > 0 {
				if err := deps.UpsertDevice(pg, validRecords); err != nil {
					logger.Errorf("Error adding message to postgres: %v", err)
				}
			}

			// DLQ for any unprocessed
			if len(unprocessed.Messages) > 0 {
				logger.Warnf("Unable to process %d device messages", len(unprocessed.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessed)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages: %v", err)
					msg.Ack()
					return
				}

				err = deps.SendToDLQ(ctx, conns.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessed.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack()
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed receiving from %s: %v", subscriptionName, err)
		}
	}
}

func upsertDeviceRMSEngine(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceRmsEngine) error {
	if len(rec) == 0 {
		return nil
	}
	const baseQuery = `
		INSERT INTO {{DeviceRMSEngine}} (
				DeviceIdentifier,
				EngineVersion,
				EngineRevision,
				PubsubTimestamp,
				UpdatedAt
			)
			VALUES %s
			ON CONFLICT (DeviceIdentifier) DO UPDATE SET
				EngineVersion       = EXCLUDED.EngineVersion,
				EngineRevision      = EXCLUDED.EngineRevision,
				PubsubTimestamp     = EXCLUDED.PubsubTimestamp,
				UpdatedAt           = CURRENT_TIMESTAMP
			WHERE EXCLUDED.PubsubTimestamp > {{DeviceRMSEngine}}.PubsubTimestamp;
	`

	keys := make([]string, 0, len(rec))
	for k := range rec {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	valueStrings := make([]string, 0, len(rec))
	valueArgs := make([]interface{}, 0, len(rec)*4)
	i := 1

	for _, k := range keys {
		r := rec[k]
		valueStrings = append(valueStrings,
			fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)", i, i+1, i+2, i+3, i+4))
		valueArgs = append(valueArgs,
			r.DeviceIdentifier,
			r.EngineVersion,
			r.EngineRevision,
			r.PubsubTimestamp,
			r.UpdatedAt,
		)
		i += 5
	}

	query := fmt.Sprintf(baseQuery, strings.Join(valueStrings, ", "))

	// Retry exec in case of deadlock
	return connect.WithDeadlockRetry(func() error {
		_, err := pg.Exec(query, valueArgs...)
		return err
	})
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:        connect.GetConnections,
	ParseAttributes:  pubsubdata.ParseAttributes,
	SendToDLQ:        etlShared.SendToDLQ,
	UnmarshalDevice:  etlShared.UnmarshalDeviceData,
	GetBatch:         bqbatch.GetBatch,
	ProcessRMSEngine: devices.ProcessRmsEngineData,
	ToBQ:             edihelper.RmsEngineToBQ,
	MarshalDevice:    etlShared.ProtoMarshal,
	UpsertDevice:     upsertDeviceRMSEngine,
})
