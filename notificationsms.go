// Package main serves as the subscription point for the device-fault topic.
// when triggered, it send sms texts to users who have access to
// the device that is in fault.
package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"
	"unicode/utf8"

	cloudEnviron "bitbucket.org/synapse-its/monitors-poc-connected-shared-cloud/environ"
	cloudSecrets "bitbucket.org/synapse-its/monitors-poc-connected-shared-cloud/secrets"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sns"
	"github.com/aws/aws-sdk-go/service/sts"
	_ "github.com/go-sql-driver/mysql"
)

// SNSMessage is the format of the message expected from the topic subscribed to.
type SNSMessage struct {
	MessageVersion string    `json:"MessageVersion"`
	MessageTime    time.Time `json:"MessageTime"`
	DeviceId       string    `json:"DeviceId"`
	FaultedAt      time.Time `json:"FaultedAt"`
	FaultReason    string    `json:"FaultReason"`
	UserDeviceId   string    `json:"UserDeviceId"`
	UserDeviceName string    `json:"UserDeviceName"`
}

const (
	WEBSITE_APPS              = "WEBSITE_APPS"
	SNS_NOTIFICATION_ROLE_ARN = "SNS_NOTIFICATION_ROLE_ARN"
)

func main() {
	lambda.Start(HandleRequest)
}

func HandleRequest(snsEvent events.SNSEvent) error {
	// load up environment vars
	websiteApps := cloudEnviron.GetEnvironmentVariable(WEBSITE_APPS, "n/a")
	if websiteApps == "n/a" {
		log.Printf("error - (%s) environment variable not defined.", WEBSITE_APPS)
	}

	SNSNotificationRoleARN := cloudEnviron.GetEnvironmentVariable(SNS_NOTIFICATION_ROLE_ARN, "n/a")
	if SNSNotificationRoleARN == "n/a" {
		log.Printf("error - (%s) environment variable not defined.", SNS_NOTIFICATION_ROLE_ARN)
	}

	for _, record := range snsEvent.Records {
		message := record.SNS.Message
		log.Printf("message from sns: (%s)\n", message) // message received from SNS

		// unmarshall the incoming message
		var incomingSNSMessage SNSMessage
		log.Printf("unmarshalling payload\n")
		err := json.Unmarshal([]byte(message), &incomingSNSMessage)
		if err != nil {
			log.Printf("error unmarshalling to json: (%v)\n", err)
			return nil // skip the sms text send
		}

		// log the values received from the SQS queue
		log.Printf("json message received:\n(%v)\n", incomingSNSMessage)

		deviceId, err := strconv.Atoi(incomingSNSMessage.DeviceId)
		if err != nil {
			log.Printf("deviceId is not legal - not sending notificaton.\n")
			return nil
		}

		// sess := session.Must(session.NewSessionWithOptions(session.Options{SharedConfigState: session.SharedConfigEnable}))
		sess := session.Must(session.NewSession())
		svcSTS := sts.New(sess)
		assumeRoleInput := &sts.AssumeRoleInput{
			RoleArn:         aws.String(SNSNotificationRoleARN),
			RoleSessionName: aws.String("SNSNotificationRoleSession"),
			DurationSeconds: aws.Int64(3600),
		}

		log.Printf("calling STS for temporary creds\n")
		assumeRoleOutput, err := svcSTS.AssumeRole(assumeRoleInput)
		if err != nil {
			log.Printf("error assuming role: (%v)\n", err)
			return nil
		}

		log.Printf("creating session from assumed role\n")
		sessFromAssumedRole, err := session.NewSession(&aws.Config{
			Credentials: credentials.NewStaticCredentials(
				*assumeRoleOutput.Credentials.AccessKeyId,
				*assumeRoleOutput.Credentials.SecretAccessKey,
				*assumeRoleOutput.Credentials.SessionToken,
			),
		})
		if err != nil {
			// Handle the session creation error
			log.Fatalf("failed to create session: %v", err)
		}

		log.Println("creating SNS client from assumed role")
		svcSNS := sns.New(sessFromAssumedRole)

		log.Println("open database connection")
		dbString, err := cloudSecrets.GetDBReadOnlyConnectionString()
		if err != nil {
			log.Printf("error opening database: (%v)\n", err)
			return nil
		}
		db, err := sql.Open("mysql", dbString)
		if err != nil {
			log.Printf("error opening database: (%v)\n", err)
			return nil
		}
		defer db.Close()

		// iterate over any users who wish to be notified.
		qry := "SELECT Mobile, IANATimezone " +
			"FROM User INNER JOIN UserDevice ON " +
			"User.Id = UserDevice.UserId " +
			"WHERE UserDevice.DeviceId = ? AND User.Mobile IS NOT NULL AND User.IsEnabled = 1 AND User.NotificationSmsEnabled = 1"
		rows, err := db.Query(qry, deviceId)
		if err != nil {
			log.Printf("unexpected error: (%v)\n", err)
			return nil
		}
		defer rows.Close()

		log.Println("iterating over mobile numbers retrieved from db")
		for rows.Next() {
			var mobilePhone, ianaTimezone string

			err := rows.Scan(&mobilePhone, &ianaTimezone)
			if err != nil {
				log.Printf("unexpected err: (%v)\n", err)
			} else {
				if len(mobilePhone) >= 10 {
					// convert utc time to the user's local time
					// var localTime time.Time

					// location, err := time.LoadLocation(ianaTimezone)
					// if err != nil {
					// 	log.Printf("error loading timezone (%s), err: (%v)\n", ianaTimezone, err)
					// 	localTime = incomingSNSMessage.FaultedAt
					// 	// continue - the time will simply be UTC as opposed to failing the conversion and not sending the sms text
					// } else {
					// 	localTime = incomingSNSMessage.FaultedAt.In(location)
					// }

					// note there is a 140 character limit on the message sent!!!!
					snsMessage := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s", incomingSNSMessage.UserDeviceId, shortenString(incomingSNSMessage.UserDeviceName, 25), shortenString(incomingSNSMessage.FaultReason, 23), websiteApps+incomingSNSMessage.DeviceId)

					// theTextMessage := fmt.Sprintf("%s: Device ID: %s, Name: %s - fault type: %s.",
					//	localTime.Format("2006-01-02 15:04:05 MST"), incomingSNSMessage.UserDeviceId, incomingSNSMessage.UserDeviceName, incomingSNSMessage.FaultReason)

					// snsMessage := theTextMessage + "\n\n" + websiteApps + incomingSNSMessage.DeviceId
					log.Printf("message length and message sent to SNS. len: (%d), msg: (%s)\n", len(snsMessage), snsMessage)
					result, err := svcSNS.Publish(&sns.PublishInput{
						Message:     aws.String(snsMessage),
						PhoneNumber: aws.String(mobilePhone),
					})
					if err != nil {
						log.Printf("failed to publish push notification: (%v)", err)
						// keep chugging
					} else {
						log.Printf("message published to %s with ID: %s", mobilePhone, *result.MessageId)
					}
				}
			}
		}
	}
	return nil
}

func shortenString(input string, maxChars int) string {
	// Return the input string if it's already within the limit
	if utf8.RuneCountInString(input) <= maxChars {
		return input
	}

	// Adjust maxChars to ensure it doesn't split in the middle of a UTF-8 encoded rune
	maxBytes := utf8.RuneCount([]byte(input))
	if maxBytes > maxChars {
		maxBytes = maxChars
	}

	// Return the shortened string with ellipsis if necessary
	shortened := string([]rune(input)[:maxBytes])
	if len(input) > maxChars {
		shortened += "..."
	}
	return shortened
}
