# Fault Notification Implementation Plan

## Overview
Implement fault notification functionality in the microservices architecture to replace the AWS smsNotification lambda function. When a fault notification message is received, the system will query eligible users, construct notification messages, and publish them to the notification delivery pipeline.

## Architecture Pattern
Following the clean architecture pattern from `/workspace/microservices/etl/processors/handlers/synapse/purgeExpired/`, this implementation will use:
- **Service Layer**: Business logic for user lookup and notification construction
- **Repository Layer**: Database operations for user queries
- **Handler Layer**: Pub/Sub message processing with dependency injection
- **Publisher Layer**: Pub/Sub message publishing abstraction

## Implementation Structure

### 1. Directory Structure
```
/workspace/microservices/etl/processors/handlers/gateway/faultNotification/
├── handler.go                    # Existing fault notification handler
├── handler_test.go              # Existing tests
├── notification_service.go      # NEW: Notification business logic
├── notification_service_test.go # NEW: Service tests
├── user_repository.go           # NEW: User lookup repository
├── user_repository_test.go      # NEW: Repository tests
├── publisher.go                 # NEW: Notification publisher
├── publisher_test.go            # NEW: Publisher tests
├── models.go                    # NEW: Data models
├── errors.go                    # NEW: Error definitions
```

### 2. Core Components

#### 2.1 Models (models.go)
```go
// User represents a user eligible for notifications
type User struct {
    ID           int64  `json:"user_id"`
    Mobile       string `json:"mobile"`
    IANATimezone string `json:"iana_timezone"`
}

// FaultNotificationData represents parsed fault data
type FaultNotificationData struct {
    DeviceID       string    `json:"device_id"`
    UserDeviceID   string    `json:"user_device_id"`
    UserDeviceName string    `json:"user_device_name"`
    FaultReason    string    `json:"fault_reason"`
    FaultedAt      time.Time `json:"faulted_at"`
}

// NotificationRequest represents a notification to be sent
type NotificationRequest struct {
    Type     string                 `json:"type"`
    Payload  map[string]interface{} `json:"payload"`
    Metadata map[string]interface{} `json:"metadata"`
}
```

#### 2.2 Error Definitions (errors.go)
```go
var (
    ErrUserLookup           = errors.New("failed to lookup eligible users")
    ErrNoEligibleUsers      = errors.New("no eligible users found for device")
    ErrNotificationConstruct = errors.New("failed to construct notification message")
    ErrNotificationPublish  = errors.New("failed to publish notification message")
    ErrFaultDataParsing     = errors.New("failed to parse fault data")
)
```

#### 2.3 User Repository (user_repository.go)
```go
type UserRepository interface {
    GetEligibleUsers(ctx context.Context, deviceID string) ([]User, error)
}

type userRepository struct {
    db connect.DatabaseExecutor
}

func (r *userRepository) GetEligibleUsers(ctx context.Context, deviceID string) ([]User, error) {
    query := `
        SELECT u.Id, u.Mobile, u.IANATimezone
        FROM {{UserDevice}} ud
        LEFT JOIN {{User}} u ON ud.UserId = u.Id
        WHERE ud.DeviceId = $1 
        AND u.Mobile IS NOT NULL 
        AND u.IsEnabled = 1 
        AND u.NotificationSmsEnabled = 1`
    
    // Implementation with error wrapping and proper result handling
}
```

#### 2.4 Notification Service (notification_service.go)
```go
type NotificationService interface {
    ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error
}

type notificationService struct {
    userRepo       UserRepository
    publisher      NotificationPublisher
    websiteAppsURL string
}

func (s *notificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
    // 1. Lookup eligible users
    users, err := s.userRepo.GetEligibleUsers(ctx, faultData.DeviceID)
    if err != nil {
        return fmt.Errorf("%w: %v", ErrUserLookup, err)
    }
    
    if len(users) == 0 {
        logger.Infof("No eligible users found for device %s", faultData.DeviceID)
        return nil // Not an error - just no notifications to send
    }
    
    // 2. Construct and publish notifications for each user
    for _, user := range users {
        notification := s.constructNotification(faultData, &user)
        if err := s.publisher.PublishNotification(ctx, notification); err != nil {
            logger.Errorf("Failed to publish notification for user %d: %v", user.ID, err)
            // Continue processing other users - don't fail entire batch
        }
    }
    
    return nil
}

func (s *notificationService) constructNotification(faultData *FaultNotificationData, user *User) *NotificationRequest {
    // Replicate AWS smsNotification message format (line 161 in notificationsms.go)
    message := fmt.Sprintf("EDIFSA\n\nDevice:\nID: %s\nName: %s\n\nMsg:\n%s\n\nDetail:\n%s", 
        faultData.UserDeviceID, 
        shortenString(faultData.UserDeviceName, 25), 
        shortenString(faultData.FaultReason, 23), 
        s.websiteAppsURL+faultData.DeviceID)
    
    return &NotificationRequest{
        Type: "sms",
        Payload: map[string]interface{}{
            "to":      user.Mobile,
            "message": message,
        },
        Metadata: map[string]interface{}{
            "device_id":        faultData.DeviceID,
            "user_device_id":   faultData.UserDeviceID,
            "user_device_name": faultData.UserDeviceName,
            "fault_reason":     faultData.FaultReason,
            "faulted_at":       faultData.FaultedAt,
            "user_id":          user.ID,
            "user_timezone":    user.IANATimezone,
        },
    }
}

// Helper function from AWS implementation
func shortenString(s string, maxLen int) string {
    if len(s) <= maxLen {
        return s
    }
    return s[:maxLen]
}
```

#### 2.5 Notification Publisher (publisher.go)
```go
type NotificationPublisher interface {
    PublishNotification(ctx context.Context, notification *NotificationRequest) error
}

type notificationPublisher struct {
    pubsubClient connect.PsClient
    topicName    string
}

func (p *notificationPublisher) PublishNotification(ctx context.Context, notification *NotificationRequest) error {
    // Marshal notification to JSON
    data, err := json.Marshal(notification)
    if err != nil {
        return fmt.Errorf("%w: failed to marshal notification: %v", ErrNotificationPublish, err)
    }
    
    // Publish to notification-alerts-sub topic
    topic := p.pubsubClient.Topic(p.topicName)
    result := topic.Publish(ctx, &pubsub.Message{
        Data: data,
        Attributes: map[string]string{
            "organizationIdentifier": "system", // Will be extracted from original message
            "topic":                  p.topicName,
        },
    })
    
    // Wait for publish result
    _, err = result.Get(ctx)
    if err != nil {
        return fmt.Errorf("%w: %v", ErrNotificationPublish, err)
    }
    
    return nil
}
```

### 3. Integration with Existing Handler

#### 3.1 Extend HandlerDeps (handler.go)
```go
type HandlerDeps struct {
    // Existing dependencies...
    Connector       ConnectorFunc
    ParseAttributes ParseAttributesFunc
    // ... other existing deps
    
    // NEW: Notification dependencies
    CreateNotificationService func(connections *connect.Connections) NotificationService
    GetWebsiteAppsURL        func() string
}
```

#### 3.2 Modify Handler Logic
Add notification processing after successful fault data processing:

```go
// After successful BQ batch add and device upsert
if rec.IsFaulted {
    // Extract fault data for notification
    faultData := &FaultNotificationData{
        DeviceID:       deviceIdentifier,
        UserDeviceID:   deviceIdentifier, // May need mapping
        UserDeviceName: "Device Name",    // Extract from device lookup
        FaultReason:    rec.Fault,
        FaultedAt:      rec.MonitorTime,
    }
    
    // Process fault notification
    notificationService := deps.CreateNotificationService(connections)
    if err := notificationService.ProcessFaultNotification(ctx, faultData); err != nil {
        logger.Errorf("Failed to process fault notification: %v", err)
        // Don't fail the main processing - log and continue
    }
}
```

### 4. Environment Configuration

#### 4.1 New Environment Variable
```bash
WEBSITE_APPS=https://www.synapse-its.app/apps/
```

#### 4.2 Configuration Function
```go
func getWebsiteAppsURL() string {
    url := os.Getenv("WEBSITE_APPS")
    if url == "" {
        return "https://www.synapse-its.app/apps/" // Default value
    }
    return url
}
```

### 5. Testing Strategy

#### 5.1 Unit Tests (100% Coverage)
- **notification_service_test.go**: Test all service methods with mocked dependencies
- **user_repository_test.go**: Test database queries with mock database executor
- **publisher_test.go**: Test Pub/Sub publishing with mock client
- **handler_test.go**: Extend existing tests to cover notification flow

#### 5.2 Test Structure Example
```go
func TestNotificationService_ProcessFaultNotification(t *testing.T) {
    tests := []struct {
        name           string
        faultData      *FaultNotificationData
        mockUsers      []User
        mockUserErr    error
        mockPublishErr error
        expectedErr    error
        expectedCalls  int
    }{
        {
            name: "successful notification processing",
            faultData: &FaultNotificationData{
                DeviceID: "device123",
                FaultReason: "Test fault",
            },
            mockUsers: []User{
                {ID: 1, Mobile: "+1234567890"},
                {ID: 2, Mobile: "+0987654321"},
            },
            expectedCalls: 2,
        },
        {
            name: "no eligible users",
            faultData: &FaultNotificationData{DeviceID: "device123"},
            mockUsers: []User{},
            expectedCalls: 0,
        },
        {
            name: "user lookup error",
            faultData: &FaultNotificationData{DeviceID: "device123"},
            mockUserErr: errors.New("db error"),
            expectedErr: ErrUserLookup,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            t.Parallel()

            // Setup mocks
            mockRepo := &MockUserRepository{}
            mockPublisher := &MockNotificationPublisher{}

            mockRepo.On("GetEligibleUsers", mock.Anything, tt.faultData.DeviceID).
                Return(tt.mockUsers, tt.mockUserErr)

            if tt.mockPublishErr != nil {
                mockPublisher.On("PublishNotification", mock.Anything, mock.Anything).
                    Return(tt.mockPublishErr)
            } else {
                mockPublisher.On("PublishNotification", mock.Anything, mock.Anything).
                    Return(nil).Times(tt.expectedCalls)
            }

            // Execute test
            service := &notificationService{
                userRepo:  mockRepo,
                publisher: mockPublisher,
                websiteAppsURL: "https://test.com/",
            }

            err := service.ProcessFaultNotification(context.Background(), tt.faultData)

            // Assert results
            if tt.expectedErr != nil {
                assert.ErrorIs(t, err, tt.expectedErr)
            } else {
                assert.NoError(t, err)
            }

            mockRepo.AssertExpectations(t)
            mockPublisher.AssertExpectations(t)
        })
    }
}
```

### 6. Integration Testing

#### 6.1 Integration Test Location
`/workspace/microservices/testing/integration/fault_notification_test.go`

#### 6.2 Integration Test Structure
```go
func TestFaultNotificationIntegration(t *testing.T) {
    // Test end-to-end flow:
    // 1. Publish fault notification message to gateway topic
    // 2. Verify notification messages are published to notification-alerts-sub
    // 3. Verify notification processor consumes and processes messages
    // 4. Verify data is persisted in BigQuery NotificationMessages table

    tests := []struct {
        name           string
        faultMessage   *gatewayv1.DeviceData
        expectedUsers  int
        expectedNotifs int
    }{
        {
            name: "fault notification with eligible users",
            faultMessage: &gatewayv1.DeviceData{
                Messages: []*gatewayv1.DeviceEntry{
                    {
                        DeviceId: "test-device-123",
                        Message:  []byte("fault-data"),
                    },
                },
            },
            expectedUsers:  2,
            expectedNotifs: 2,
        },
        {
            name: "fault notification with no eligible users",
            faultMessage: &gatewayv1.DeviceData{
                Messages: []*gatewayv1.DeviceEntry{
                    {
                        DeviceId: "no-users-device",
                        Message:  []byte("fault-data"),
                    },
                },
            },
            expectedUsers:  0,
            expectedNotifs: 0,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup test environment
            // Publish message and verify end-to-end flow
        })
    }
}
```

### 7. Implementation Steps

#### 7.1 Phase 1: Core Components
1. **Create models.go** with data structures
2. **Create errors.go** with error definitions
3. **Implement user_repository.go** with database queries
4. **Write comprehensive unit tests** for repository

#### 7.2 Phase 2: Business Logic
1. **Implement notification_service.go** with business logic
2. **Implement publisher.go** for Pub/Sub publishing
3. **Write comprehensive unit tests** for service and publisher

#### 7.3 Phase 3: Integration
1. **Extend existing handler.go** to integrate notification processing
2. **Add environment variable configuration**
3. **Update existing handler tests**

#### 7.4 Phase 4: Testing
1. **Create integration tests** in testing directory
2. **Verify end-to-end message flow**
3. **Test BigQuery persistence**
4. **Validate error handling and logging**

### 8. Acceptance Criteria Verification

- ✅ Users with device access and notifications enabled are correctly queried
- ✅ No messages sent if no users qualify
- ✅ Fault data formatted to match AWS smsNotification behavior
- ✅ One notification message per eligible user using expected schema
- ✅ Messages published to notification-alerts-sub topic
- ✅ Integration test verifies message format, publishing, and BigQuery persistence
- ✅ Errors are logged; failures don't crash the system
- ✅ 100% unit test coverage for new code paths

### 9. Dependencies and Configuration

#### 9.1 Required Environment Variables
```bash
WEBSITE_APPS=https://www.synapse-its.app/apps/
```

#### 9.2 Pub/Sub Topics
- **Input**: Existing fault notification topic (gateway messages)
- **Output**: `notification-alerts-sub` topic

#### 9.3 Database Tables
- **UserDevice**: Device access mapping
- **User**: User information and notification preferences

### 10. Production Deployment Considerations

#### 10.1 Backward Compatibility
- Implementation maintains existing fault notification processing
- New notification functionality is additive, not replacing existing logic
- Failures in notification processing don't affect core fault data processing

#### 10.2 Monitoring and Observability
- Add metrics for notification processing success/failure rates
- Log notification attempts and outcomes for debugging
- Monitor Pub/Sub topic health and message processing latency

#### 10.3 Error Handling Strategy
- Graceful degradation: Core fault processing continues even if notifications fail
- Retry logic for transient Pub/Sub publishing failures
- Dead letter queue handling for persistent notification failures

### 11. Future Enhancements

#### 11.1 Notification Channels
- Email notifications (extend NotificationRequest type)
- Push notifications for mobile apps
- Webhook notifications for external systems

#### 11.2 User Preferences
- Per-device notification settings
- Notification frequency limits (rate limiting)
- Notification scheduling (quiet hours)

#### 11.3 Message Customization
- User-specific message templates
- Localization support for different languages
- Rich message formatting (HTML for email)

This implementation plan follows the established clean architecture patterns, maintains 100% test coverage, and integrates seamlessly with the existing fault notification processing pipeline while adding the new notification functionality.
