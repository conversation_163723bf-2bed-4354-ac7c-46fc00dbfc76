-- 1) FaultLogs
INSERT INTO {{FaultLogs}}
  (organizationidentifier,
   softwaregatewayid,
   tz,
   topic,
   pubsubtimestamp,
   pubsubid,
   deviceid,
   rawlogmessages,
   loguuid)
VALUES (
  'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC',
  'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f',
  'UTC',
  'topic1',
  TIMESTAMP '2025-05-01 10:00:00 UTC',
  'PS1',
  '3fbe60189eef4bb6ab9c03663cc7b3ba',
  STRUCT(
    -- two raw resets
    [CAST('reset1_bytes' AS BYTES), CAST('reset2_bytes' AS BYTES)],
    -- one prev-fail
    [CAST('prevfail1_bytes' AS BYTES)],
    -- two AC-line events
    [CAST('ac1_bytes' AS BYTES), CAST('ac2_bytes' AS BYTES)],
    -- one fault-signal sequence
    [CAST('faultsig1_bytes' AS BYTES)],
    -- one configuration
    [CAST('config1_bytes' AS BYTES)]
  ),
  'event1-uuid'
);


-- 2) LogMonitorReset
INSERT INTO {{LogMonitorReset}}
  (organizationidentifier,
   softwaregatewayid,
   tz,
   topic,
   pubsubtimestamp,
   pubsubid,
   deviceid,
   header,
   devicemodel,
   records,
   rawmessage,
   loguuid)
VALUES (
  'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC',
  'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f',
  'UTC',
  'topic1',
  TIMESTAMP '2025-05-01 10:00:00 UTC',
  'PS1',
  '3fbe60189eef4bb6ab9c03663cc7b3ba',
  STRUCT(
    1,      -- commversion
    1001,   -- model
    2001,   -- firmwareversion
    3001,   -- firmwarerevision
    1,      -- monitorid
    TRUE,   -- volt220
    FALSE,  -- voltdc
    TRUE,   -- mainsdc
    10,     -- powerdownlevel
    20,     -- blackoutlevel
    4       -- maxchannels
  ),
  'ModelX',
  [  -- two reset events
    STRUCT(
      TIMESTAMP '2025-05-01 10:01:00 UTC' as eventtimestamp,
      'ManualReset' as resettype
    ),
    STRUCT(
      TIMESTAMP '2025-05-01 10:15:30 UTC' as eventtimestamp,
      'AutoReset' as resettype
    )
  ],
  CAST('monitorreset1_raw' AS BYTES),
  'event1-uuid'
);

-- 3) logPreviousFail
INSERT INTO {{logPreviousFail}}
  (organizationidentifier,
   softwaregatewayid,
   tz,
   topic,
   pubsubtimestamp,
   pubsubid,
   deviceid,
   header,
   devicemodel,
   records,
   rawmessage,
   loguuid)
VALUES (
  'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC',
  'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f',
  'UTC',
  'topic1',
  TIMESTAMP '2025-05-01 10:00:00 UTC',
  'PS1',
  '3fbe60189eef4bb6ab9c03663cc7b3ba',
  STRUCT(
    1,1001,2001,3001,1,TRUE,FALSE,TRUE,10,20,4
  ),
  'ModelX',
  [  -- ← ARRAY<STRUCT<datetime TIMESTAMP, fault STRING, …>>
    STRUCT(
      TIMESTAMP '2025-05-01 10:03:00 UTC'    AS datetime,
      'Overheat'                            AS fault,
      'OK'                                  AS acline,
      'OK'                                  AS t48vdcsignalbus,
      'ENABLED'                             AS redenable,
      'COIL_OK'                             AS mccoilee,
      'SF1'                                 AS specialfunction1,
      'SF2'                                 AS specialfunction2,
      'MON_OK'                              AS wdtmonitor,
      'IN24V'                               AS t24vdcinput,
      75                                    AS temperature,
      TRUE                                  AS lsflashbit,
      [TRUE, FALSE, TRUE, FALSE]           AS faultstatus,
      [TRUE, TRUE, TRUE, TRUE]             AS channelgreenstatus,
      [FALSE, TRUE, FALSE, TRUE]           AS channelyellowstatus,
      [FALSE, FALSE, TRUE, FALSE]          AS channelredstatus,
      [TRUE, FALSE, TRUE, FALSE]           AS channelwalkstatus,
      [FALSE, TRUE, TRUE, TRUE]            AS channelgreenfieldcheckstatus,
      [TRUE, TRUE, FALSE, FALSE]           AS channelyellowfieldcheckstatus,
      [FALSE, TRUE, TRUE, TRUE]            AS channelredfieldcheckstatus,
      [TRUE, TRUE, TRUE, TRUE]             AS channelwalkfieldcheckstatus,
      [FALSE, TRUE, FALSE, TRUE]           AS channelgreenrecurrentpulsestatus,
      [TRUE, FALSE, TRUE, FALSE]           AS channelyellowrecurrentpulsestatus,
      [FALSE, TRUE, FALSE, TRUE]           AS channelredrecurrentpulsestatus,
      [TRUE, FALSE, TRUE, FALSE]           AS channelwalkrecurrentpulsestatus,
      [110,115,120,125]                    AS channelgreenrmsvoltage,
      [112,117,122,127]                    AS channelyellowrmsvoltage,
      [108,113,118,123]                    AS channelredrmsvoltage,
      [105,110,115,120]                    AS channelwalkrmsvoltage,
      [FALSE, TRUE, FALSE, TRUE]           AS nextconflictingchannels,
      [TRUE, FALSE, TRUE, FALSE]           AS channelredcurrentstatus,
      [FALSE, TRUE, FALSE, TRUE]           AS channelyellowcurrentstatus,
      [TRUE, TRUE, FALSE, TRUE]            AS channelgreencurrentstatus,
      [5,6,7,8]                            AS channelredrmscurrent,
      [4,3,2,1]                            AS channelyellowrmscurrent,
      [10,20,30,40]                        AS channelgreenrmscurrent
    )
  ],
  CAST('prevfail1_raw' AS BYTES),
  'event1-uuid'
);


-- 4) logACLineEvent
INSERT INTO {{logACLineEvent}}
  (organizationidentifier,
   softwaregatewayid,
   tz,
   topic,
   pubsubtimestamp,
   pubsubid,
   deviceid,
   header,
   devicemodel,
   record,
   voltagetype,
   rawmessage,
   loguuid)
VALUES (
  'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC',
  'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f',
  'UTC',
  'topic1',
  TIMESTAMP '2025-05-01 10:00:00 UTC',
  'PS1',
  '3fbe60189eef4bb6ab9c03663cc7b3ba',
  STRUCT(1,1001,2001,3001,1,TRUE,FALSE,TRUE,10,20,4),
  'ModelX',
  [  -- two AC-line events
    STRUCT('Undervoltage' AS eventtype, TIMESTAMP '2025-05-01 10:02:00 UTC' AS datetime, 110 AS linevoltagerms, 59 AS linefrequencyhz),
    STRUCT('Overvoltage' AS eventtype,  TIMESTAMP '2025-05-01 10:05:00 UTC' AS datetime, 130 AS linevoltagerms, 61 AS linefrequencyhz)
  ],
  1,
  CAST('ac1_raw' AS BYTES),
  'event1-uuid'
);

-- 5) logFaultSignalSequence
INSERT INTO {{logFaultSignalSequence}}
  (organizationidentifier,
   softwaregatewayid,
   tz,
   topic,
   pubsubtimestamp,
   pubsubid,
   deviceid,
   header,
   devicemodel,
   records,
   rawmessage,
   loguuid)
VALUES (
  'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC',
  'd5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f',
  'UTC',
  'topic1',
  TIMESTAMP '2025-05-01 10:00:00 UTC',
  'PS1',
  '3fbe60189eef4bb6ab9c03663cc7b3ba',
  STRUCT(1,1001,2001,3001,1,TRUE,FALSE,TRUE,10,20,4),
  'ModelX',
  [  -- one fault-signal-sequence entry
    STRUCT(
      CAST('trace1_bytes' AS BYTES) as tracerawbytes,
      'TypeA' as faulttype,
      [  -- two trace-buffer records
        STRUCT(CAST('buf1' AS BYTES) as bufferrawbytes, 12345 as timestamp, [TRUE, FALSE] as reds, [FALSE, TRUE] as yellows, [TRUE, TRUE] as greens, [FALSE, FALSE] as walks, TRUE as ee_sf_re, 230 as acvoltage),
        STRUCT(CAST('buf2' AS BYTES) as bufferrawbytes, 12346 as timestamp, [FALSE, TRUE] as reds, [TRUE, FALSE] as yellows, [FALSE, TRUE] as greens, [TRUE, FALSE] as walks, FALSE as ee_sf_re, 240 as acvoltage)
      ] as records
    )
  ],
  CAST('faultsig1_raw' AS BYTES),
  'event1-uuid'
);
