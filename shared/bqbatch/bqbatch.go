package bqbatch

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"sync"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/schemas"
)

// This type is used as a key in which to store the batch in the context.  It
// exists simply to make the linters happy.
type contextBatchKey string

// This constant is the identifier used in the context to store the connection information.
const BatchKey contextBatchKey = "bqbatch"

// PubSub topic name for DLQ backup incase bigquery is down.
const PubsubDLQTopic string = "dlq-bqbatch"

var (
	// The maximum number of rows that a queue should hold before it flushes.
	MaxRows = 5000
	// The maximum time that a queue should wait before it flushes.
	MaxWait = 10 * time.Second
	// The number of retries allowed if load fails.
	MaxAttempts = 5

	// Allow overriding time.Sleep for testing
	timeSleep = time.Sleep
)

// Batcher is the public interface.  You Register types, Add rows, then Shutdown.
type Batcher interface {
	// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
	Register(rowExample any, table string, cfg QueueConfig) error
	// Add enqueues a row; it'll dispatch to whichever queue was registered for that type.
	Add(row any) error
	// Immediately attempt to load a previously failed batch.
	LoadBatch(batch FailedBatch) error
	// Shutdown flushes all queues and stops accepting new rows.
	Shutdown() error
}

// QueueConfig holds per-queue flush criteria.
type QueueConfig struct {
	MaxSize       int           // number of rows to trigger flush
	FlushInterval time.Duration // time to trigger flush
}

// batcher is the concrete implementation of Batcher.
type batcher struct {
	exec        connect.BigQueryExecutorInterface
	psClient    connect.PsClient
	cfgs        map[string]QueueConfig
	queues      map[string]*queue
	typeToTable map[reflect.Type]string
	mu          sync.Mutex
	shutdown    bool
}

// queue represents a per-table buffer and its worker.
type queue struct {
	table    string
	cfg      QueueConfig
	schema   bigquery.Schema
	exec     connect.BigQueryExecutorInterface
	psClient connect.PsClient

	// Used to stop the worker.
	ctrlCtx context.Context
	cancel  context.CancelFunc

	// Used for BQ calls.
	loadCtx context.Context

	rows []bigquery.ValueSaver
	mu   sync.Mutex
	wg   sync.WaitGroup
}

func init() {
	// This is a in-production safety check.  If it fails, the process will be
	// terminated.  It is not a test, but a sanity check.

	// Validate that all the default table/struct pairings are valid.
	for _, q := range queues {
		if q.table == "" {
			logger.Fatalf("Default Batcher failed validation: table name is empty")
		}
		if _, _, err := getValidType(q.rowExample); err != nil {
			logger.Fatalf("Default Batcher failed validation: %v", err)
		}
	}

	// Validate that the batcher initializes correctly.
	_, err := newDefault(&bqexecutor.FakeBigQueryExecutor{
		Ctx: context.Background(),
	}, nil)
	if err != nil {
		logger.Fatalf("Default Batcher failed validation: %v", ErrDefaultBatcher)
	}

	// Override the MaxRows and MaxWait values if they are overridden in the
	// environment variables.
	if maxRows := os.Getenv("BQBATCH_MAX_ROWS"); maxRows != "" {
		maxRowsInt, err := strconv.Atoi(maxRows)
		if err == nil && maxRowsInt > 0 {
			MaxRows = maxRowsInt
		}
	}
	if maxWait := os.Getenv("BQBATCH_MAX_WAIT"); maxWait != "" {
		maxWaitInt, err := strconv.Atoi(maxWait)
		if err == nil && maxWaitInt > 0 {
			MaxWait = time.Duration(maxWaitInt) * time.Second
		}
	}
	if maxAttempts := os.Getenv("BQBATCH_MAX_ATTEMPTS"); maxAttempts != "" {
		maxAttemptsInt, err := strconv.Atoi(maxAttempts)
		if err == nil && maxAttemptsInt > 0 {
			MaxAttempts = maxAttemptsInt
		}
	}
}

// Helper function that assembles a batch handler for use in the data-core
// microservices.  This function registers the default table/struct pairings.
func NewDefault(exec connect.BigQueryExecutorInterface, psclient connect.PsClient) Batcher {
	// To be clear, ignoring the error is fine here because the unit test has
	// already verified that all the default table/struct pairings are valid.
	// This is just a convenience function for the data-core microservices.
	// It is also verified in the init() function.
	b, _ := newDefault(exec, psclient)
	return b
}

// This is a list of the default table/struct pairings.  It is validated in a
// unit test that all pairings are valid.
var queues = []struct {
	rowExample any
	table      string
}{
	{schemas.EdiRawMessages{}, schemas.T_EdiRawMessages},
	{schemas.DlqMessages{}, schemas.T_DlqMessages},
	{schemas.RmsEngine{}, schemas.T_RmsEngine},
	{schemas.MonitorName{}, schemas.T_MonitorName},
	{schemas.MacAddress{}, schemas.T_MacAddress},
	{schemas.RmsData{}, schemas.T_RmsData},
	{schemas.FaultNotification{}, schemas.T_FaultNotification},
	{schemas.GatewayPerformanceStatistics{}, schemas.T_GatewayPerformanceStatistics},
	{schemas.GatewayLogMessage{}, schemas.T_GatewayLogMessage},
	{schemas.FaultLogs{}, schemas.T_FaultLogs},
	{schemas.LogMonitorReset{}, schemas.T_LogMonitorReset},
	{schemas.LogPreviousFail{}, schemas.T_logPreviousFail},
	{schemas.LogACLineEvent{}, schemas.T_logACLineEvent},
	{schemas.LogFaultSignalSequence{}, schemas.T_logFaultSignalSequence},
	{schemas.LogConfiguration{}, schemas.T_logConfiguration},
	{schemas.NotificationMessages{}, schemas.T_NotificationMessages},
}

// Helper function (used by the unit tests) that ensures that all registered
// table/struct pairings are valid.  This exists so that the public
// NewDefault() function can be used cleanly.
func newDefault(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) (Batcher, error) {
	b := New(exec, psClient)

	defaultConfig := QueueConfig{
		MaxSize:       1000,
		FlushInterval: 1 * time.Second,
	}

	for _, q := range queues {
		// Errors are ignored because a unit test has already verified that all
		// the default table/struct pairings are valid and also because the module
		// will fail on init() if any of the default table/struct pairings are
		// invalid.
		_ = b.Register(q.rowExample, q.table, defaultConfig)
	}

	return b, nil
}

// New returns an empty batcher; you must Register() at least once.
func New(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) Batcher {
	return &batcher{
		exec:        exec,
		psClient:    psClient,
		cfgs:        make(map[string]QueueConfig),
		queues:      make(map[string]*queue),
		typeToTable: make(map[reflect.Type]string),
	}
}

// Helper function that validates that the provided type is valid for use in
// the batcher.
func getValidType(t any) (reflect.Type, bigquery.Schema, error) {
	// Figure out the underlying struct type.
	tType := reflect.TypeOf(t)
	if tType == nil {
		return nil, nil, ErrNotStruct
	}
	if tType.Kind() == reflect.Ptr {
		tType = tType.Elem()
	}
	if tType.Kind() != reflect.Struct {
		return nil, nil, ErrNotStruct
	}

	// Verify that the schema can be inferred.
	schema, err := bigquery.InferSchema(t)
	if err != nil {
		return nil, nil, err
	}
	return tType, schema, nil
}

// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
// This is a required step before you can Add() rows.
// The rowExample parameter is used to infer the schema of the table.
func (b *batcher) Register(rowExample any, table string, cfg QueueConfig) error {
	t, schema, err := getValidType(rowExample)
	if err != nil {
		return err
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	if b.shutdown {
		return ErrBatcherIsShutDown
	}
	if _, exists := b.typeToTable[t]; exists {
		return fmt.Errorf("type %s already registered", t)
	}

	// Remember the mapping.
	b.typeToTable[t] = table
	b.cfgs[table] = cfg

	// Create the queue.
	b.queues[table] = newQueue(b.exec, b.psClient, table, cfg, schema)
	return nil
}

func (b *batcher) Add(row any) error {
	// Figure out the underlying struct type.
	t := reflect.TypeOf(row)
	if t == nil {
		return ErrNotStruct
	}
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return ErrNotStruct
	}

	// Lock the *batcher*.
	b.mu.Lock()

	// Validate the state of the batcher and whether the type is registered.
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	table, ok := b.typeToTable[t]
	if !ok {
		b.mu.Unlock()
		return ErrUnknownType
	}
	q, ok := b.queues[table]
	b.mu.Unlock()
	if !ok {
		return fmt.Errorf("%w (%s)", ErrUnknownType, table)
	}
	// Batch lock is no longer held.

	// Lock the *queue*.
	q.mu.Lock()

	// Append the row to the queue.
	q.rows = append(q.rows, &bigquery.StructSaver{
		Struct: row,
		Schema: q.schema,
	})

	// Check if we need to flush.
	sz := len(q.rows)
	q.mu.Unlock()
	if sz >= q.cfg.MaxSize {
		q.flushAsync()
	}

	return nil
}

// Shutdown signals all queue workers to flush and stop.
func (b *batcher) Shutdown() error {
	b.mu.Lock()
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	b.shutdown = true
	// Kick off all the workers to flush.
	for _, q := range b.queues {
		q.cancel()
	}
	b.mu.Unlock()

	// Wait for them all.
	for _, q := range b.queues {
		q.wg.Wait()
	}
	return nil
}

// newQueue initializes a queue and starts its worker.
func newQueue(exec connect.BigQueryExecutorInterface, psClient connect.PsClient, table string, cfg QueueConfig, schema bigquery.Schema) *queue {
	// ctrlCtx controls the worker's lifecycle
	ctrlCtx, cancel := context.WithCancel(exec.GetContext())
	// loadCtx is the "root" context for BQ operations (loader.Run / job.Wait)
	loadCtx := exec.GetContext()

	q := &queue{
		table:    table,
		cfg:      cfg,
		schema:   schema,
		exec:     exec,
		psClient: psClient,
		ctrlCtx:  ctrlCtx,
		cancel:   cancel,
		loadCtx:  loadCtx,
		rows:     make([]bigquery.ValueSaver, 0, cfg.MaxSize),
	}
	q.wg.Add(1)
	go q.worker()
	return q
}

// worker handles timed flushes and shutdown.
func (q *queue) worker() {
	defer q.wg.Done()
	ticker := time.NewTicker(q.cfg.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			q.flushAsync()
		case <-q.ctrlCtx.Done():
			q.flushSync()
			return
		}
	}
}

// flushAsync triggers a non-blocking flush.
func (q *queue) flushAsync() {
	// copy rows under lock
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()
	// run load in goroutine
	go q.load(batch, 0)
}

// flushSync does a blocking flush (for shutdown).
func (q *queue) flushSync() {
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()
	q.load(batch, 0)
}

// load performs the BigQuery batch load for the given rows.
func (q *queue) load(rows []bigquery.ValueSaver, retryCount int) {
	if retryCount > MaxAttempts {
		logger.Error("Max retry attempts reached, dumping records to logs")
		logger.Error(rows)
		return
	}

	// Get the table name in the correct namespace
	tablename := connect.CombineTableNamespace(q.exec.GetConfig().Namespace, q.table)

	// encode rows to JSON-lines
	var buf bytes.Buffer
	enc := json.NewEncoder(&buf)

	var rowMaps []map[string]bigquery.Value
	for _, r := range rows {
		// extract the actual row data
		rowMap, _, err := r.Save()
		if err != nil {
			logger.Errorf("Failed to Save() row: %v", err)
			return
		}
		rowMaps = append(rowMaps, rowMap)
		if err := enc.Encode(rowMap); err != nil {
			logger.Errorf("Failed to JSON-encode rowMap: %v", err)
			return
		}
	}

	// set up reader source
	rs := bigquery.NewReaderSource(&buf)
	rs.SourceFormat = bigquery.JSON

	// configure loader
	dataset := q.exec.GetClient().Dataset(q.exec.GetConfig().DBName)
	t := dataset.Table(tablename)
	loader := t.LoaderFrom(rs)
	loader.WriteDisposition = bigquery.WriteAppend

	// run job with retry
	var lastErr error
	for i := range MaxAttempts {
		job, err := loader.Run(q.loadCtx)
		if err != nil {
			lastErr = fmt.Errorf("loader.Run failed: %w", err)
		} else {
			status, err := job.Wait(q.loadCtx)
			if err != nil {
				lastErr = fmt.Errorf("job.Wait failed: %w", err)
			} else if statusErr := status.Err(); statusErr != nil {
				lastErr = fmt.Errorf("BQ job status error: %w", statusErr)
			} else {
				return // success
			}
		}

		// exponential backoff
		logger.Infof("BqBatch load failed batch for table %s, retyring in %d seconds", tablename, 1<<i)
		timeSleep(1 << i * time.Second)
	}

	// send to pubsub DLQ
	logger.Errorf("BqBatch failed after %d retries. Sending to pubsub batch DLQ", MaxAttempts)

	// convert bigquery.Values to any
	psData := make([]map[string]any, 0, len(rowMaps))
	for _, row := range rowMaps {
		rowMap := make(map[string]any, len(row))
		for k, v := range row {
			rowMap[k] = v
		}
		psData = append(psData, rowMap)
	}

	batch := FailedBatch{
		Table:      q.table,
		Timestamp:  time.Now().UTC(),
		Error:      lastErr.Error(),
		Rows:       psData,
		RetryCount: retryCount,
	}

	data, _ := json.Marshal(batch)
	topic := q.psClient.Topic(PubsubDLQTopic)

	for i := range MaxAttempts {
		res := topic.Publish(q.loadCtx, &pubsub.Message{
			Data: data,
			Attributes: map[string]string{
				"table":       batch.Table,
				"retry_count": strconv.Itoa(batch.RetryCount),
				"error":       lastErr.Error(),
			},
		})
		_, err := res.Get(q.loadCtx)
		if err == nil {
			return
		}

		// exponential backoff
		logger.Warnf("BqBatch backup pubsub DLQ failed, retyring in %d seconds", 1<<i)
		timeSleep(1 << i * time.Second)
	}

	logger.Error("BqBatch failed to send to DLQ backup. Dumping batch to logs...")
	logger.Error(data)
}

// Load batch immediately loads a FailedBatch and executes a load.
func (b *batcher) LoadBatch(batch FailedBatch) error {
	q, ok := b.queues[batch.Table]
	if !ok {
		return fmt.Errorf("unknown table: %s", batch.Table)
	}

	var savers []bigquery.ValueSaver
	for _, row := range batch.Rows {
		savers = append(savers, &bigquery.StructSaver{
			Struct: row,
			Schema: q.schema,
		})
	}

	go q.load(savers, batch.RetryCount)
	return nil
}

// Add the Connections to the context.
func WithBatch(ctx context.Context, batch Batcher) context.Context {
	return context.WithValue(ctx, BatchKey, batch)
}

// Get the Connections from the context.
// checkConnections is optional
var GetBatch = func(ctx context.Context) (Batcher, error) {
	batch, ok := ctx.Value(BatchKey).(Batcher)
	if !ok {
		return nil, ErrBatchContext
	}

	return batch, nil
}
