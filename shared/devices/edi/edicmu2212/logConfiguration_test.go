package edicmu2212

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// computeChecksum calculates a valid checksum for a message.
func computeChecksum(msg []byte) byte {
	var sum int32
	for i := 0; i < len(msg)-1; i++ {
		sum += int32(msg[i])
	}
	// Get the least significant byte of the sum
	leastSignificant := sum & 0xff
	// Take the 1s complement of the least significant byte
	return byte(^leastSignificant)
}

// createByteMsg creates a valid byte message for testing.
func createByteMsg(numRecords int, recordSize int, firmware uint16) []byte {
	// Create a message with exactly the expected length
	msgSize := HeaderLength + (numRecords * recordSize) + HeaderOffset
	msg := make([]byte, msgSize)
	msg[HeaderLength] = byte(numRecords) // Number of records

	// Fill timestamp bytes with valid BCD values
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize)
		if offset+ConfigTimeSecOffset < len(msg) {
			msg[offset+ConfigTimeMonOffset] = 0x05  // May (BCD: 05)
			msg[offset+ConfigTimeDayOffset] = 0x15  // 15th (BCD: 15)
			msg[offset+ConfigTimeYearOffset] = 0x25 // 2025 (BCD: 25)
			msg[offset+ConfigTimeHourOffset] = 0x16 // 16:00 (BCD: 16)
			msg[offset+ConfigTimeMinOffset] = 0x27  // 27 minutes (BCD: 27)
			msg[offset+ConfigTimeSecOffset] = 0x00  // 0 seconds (BCD: 00)
		}
	}

	// Fill permissive data
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize) + PermsOffset
		if offset+62 < len(msg) {
			for j := 0; j < 62; j++ {
				msg[offset+j] = 0x01 // Enable one permissive channel
			}
		}
	}

	// Fill bitmap data (LOS, Multiple, Clearance, etc.)
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize)
		if offset+DarkMapOffset+16 < len(msg) {
			for j := 0; j < 16; j++ {
				msg[offset+LOSOffset+j] = 0x01
				msg[offset+DarkMapOffset+j] = 0x01
				msg[offset+MultipleOffset+j] = 0x01
				msg[offset+ClearanceOffset+j] = 0x01
				msg[offset+YDOffset+j] = 0x01
				msg[offset+FCOffset+j] = 0x01
			}
		}
	}

	// Fill virtual channel data
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize)
		if offset+VCoffset+12 < len(msg) {
			for j := 0; j < 12; j++ {
				msg[offset+VCoffset+j] = 0x21 // Channel 1, Red
			}
		}
	}

	// Fill unit data
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize)
		if offset+MiscOffset+1 < len(msg) {
			msg[offset+MinFlashOffset] = 10    // Minimum flash
			msg[offset+V12EnableOffset] = 0x07 // X24VoltInhibit + X24VoltInhibit + CvmLatchEnable
			// msg[offset+V48EnableOffset] = 0x04   // WatchdogEnableSwitch
			// msg[offset+HDFU2EnableOffset] = 0x02 // CvmLatchEnable
			msg[offset+HDSPEnableOffset] = 0x01 // WalkEnableTs1
			msg[offset+MiscOffset] = 0x07       // CMU Address = 3, RP Detect = 1
			msg[offset+DatakeyCRCOffset] = 0x34
			msg[offset+DatakeyCRCOffset+1] = 0x12
			msg[offset+EdiCRCOffset] = 0x78
			msg[offset+EdiCRCOffset+1] = 0x56
		}
	}

	// Fill FYA data (for firmware >= 1.5)
	if firmware > 0x14 {
		fyaCount := 6
		if firmware > 0x19 {
			fyaCount = 8
		}
		for i := 0; i < numRecords; i++ {
			offset := HeaderLength + 1 + (i * recordSize)
			fyaOLP := CFYA_OLPOffset
			fyaGa := CFYA_GaOffset
			fyaOPP := CFYA_OPPOffset
			if firmware > 0x20 {
				fyaOLP = CFYA_OLPOffset2
				fyaGa = CFYA_GaOffset2
				fyaOPP = CFYA_OPPOffset2
			}
			if offset+fyaOPP+fyaCount < len(msg) {
				for j := 0; j < fyaCount; j++ {
					msg[offset+fyaOLP+j] = 0x61 // Enabled, FlashRate, Ch1
					msg[offset+fyaGa+j] = 0x41  // RedYellowEnable, Ch1
					msg[offset+fyaOPP+j] = 0x41 // YTrap, Ch1
				}
			}
		}
	}

	// Fill current sense data
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + 1 + (i * recordSize)
		if offset+CSUGThreshOffset+32 < len(msg) {
			for j := 0; j < 4; j++ {
				msg[offset+CSUROffset+j] = 0x01
				msg[offset+CSUYOffset+j] = 0x01
				msg[offset+CSUGOffset+j] = 0x01
			}
			for j := 0; j < 32; j++ {
				msg[offset+CSURThreshOffset+j] = 130 // Above ScalePoint
				msg[offset+CSUYThreshOffset+j] = 50  // Below ScalePoint
				msg[offset+CSUGThreshOffset+j] = 50  // Below ScalePoint
			}
		}
	}

	// Compute and set the checksum
	msg[len(msg)-1] = computeChecksum(msg)

	return msg
}

// Test_LogConfiguration_Scenarios tests various scenarios for the LogConfiguration function
func Test_LogConfiguration_Scenarios(t *testing.T) {
	// Setup common inputs
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	// Define test case structure according to coverage rules
	type testCase struct {
		name        string                                                  // Required: descriptive test name
		numRecords  int                                                     // Input: number of records in the message
		recordSize  int                                                     // Input: size of each record
		firmware    uint16                                                  // Input: firmware version
		setupFn     func([]byte)                                            // Optional: setup function to modify the message
		wantErr     bool                                                    // Whether error is expected
		expectedErr string                                                  // Expected error message if any
		validateFn  func(*testing.T, *helper.ConfigurationChangeLogRecords) // Function to validate the result
	}

	// Define test cases
	tests := []testCase{
		{
			name:       "ValidInput_FirmwareBelow1_5",
			numRecords: 1,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn:    nil,
			wantErr:    false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				// Verify the result
				assert.NotNil(t, result)
				assert.Equal(t, DeviceModel, result.DeviceModel)
				assert.Len(t, result.Record, 1)

				record := result.Record[0]
				// Check that we have a valid timestamp
				assert.NotZero(t, record.DateTime)
				// Check that we have permissives data
				assert.NotEmpty(t, record.Ch01Permissives)
				// Check that we have RedFailEnable data
				assert.NotEmpty(t, record.RedFailEnable)
				// Check other fields
				assert.NotEmpty(t, record.MinimumFlashTime)
				assert.Equal(t, "18", record.RedFaultTiming)
				assert.Equal(t, "4660", record.CheckValue)
				// Don't check the exact value of ChangeSource as it may vary
				assert.Empty(t, record.FlashingYellowArrows) // No FYA for firmware < 1.5
			},
		},
		{
			name:       "MultipleRecords",
			numRecords: 2,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn:    nil,
			wantErr:    false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				assert.NotNil(t, result)
				assert.Len(t, result.Record, 2)

				// Verify both records have valid data
				for i := 0; i < 2; i++ {
					record := result.Record[i]
					assert.NotZero(t, record.DateTime)
					assert.NotEmpty(t, record.Ch01Permissives)
					assert.NotEmpty(t, record.MinimumFlashTime)
				}
			},
		},
		{
			name:       "MaximumRecords",
			numRecords: 10,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn:    nil,
			wantErr:    false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				assert.NotNil(t, result)
				assert.Len(t, result.Record, 10)
			},
		},
		{
			name:       "PermissivesEdgeCase",
			numRecords: 1,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn: func(byteMsg []byte) {
				// Modify the permissives data to have fewer than 15 channels
				// We'll set all permissives bytes to 0 except for the first few
				for i := 5; i < 62; i++ {
					byteMsg[HeaderLength+1+PermsOffset+i] = 0x00
				}

				// Recompute the checksum after modifying the data
				byteMsg[len(byteMsg)-1] = computeChecksum(byteMsg)
			},
			wantErr: false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				assert.NotNil(t, result)
				assert.Len(t, result.Record, 1)

				// Verify the first channel has data
				assert.NotEmpty(t, result.Record[0].Ch01Permissives)
			},
		},
		{
			name:       "InvalidByteLength",
			numRecords: 1,
			recordSize: 0, // This will create a message that's too short
			firmware:   0x10,
			setupFn: func(byteMsg []byte) {
				// Just enough to set the number of records, but not enough for the full message
				newMsg := make([]byte, HeaderLength+1)
				newMsg[HeaderLength] = 1 // Set number of records to 1

				// Copy the new message over the original
				copy(byteMsg, newMsg)
			},
			wantErr:     true,
			expectedErr: "byte length",
			validateFn:  nil,
		},
		{
			name:       "InvalidChecksum",
			numRecords: 1,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn: func(byteMsg []byte) {
				// Invalidate the checksum by changing it
				byteMsg[len(byteMsg)-1] = byteMsg[len(byteMsg)-1] + 1
			},
			wantErr:     true,
			expectedErr: "checksum",
			validateFn:  nil,
		},
		{
			name:       "InvalidTimestamp",
			numRecords: 1,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn: func(byteMsg []byte) {
				byteMsg[HeaderLength+1+ConfigTimeMonOffset] = 0x13 // Invalid month (13)

				// Recompute the checksum after modifying the data
				byteMsg[len(byteMsg)-1] = computeChecksum(byteMsg)
			},
			wantErr:     true,
			expectedErr: "month",
			validateFn:  nil,
		},
		{
			name:       "ZeroRecords",
			numRecords: 0,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn:    nil,
			wantErr:    false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				assert.NotNil(t, result)
				assert.Empty(t, result.Record)
			},
		},
	}

	// Add test cases for FYA functionality
	tests = append(tests, testCase{
		name:       "ValidInput_Firmware1_5_MockFYA",
		numRecords: 1,
		recordSize: CFCMU2rmsLogLengthFYA,
		firmware:   0x16,
		setupFn:    nil,
		wantErr:    false,
		validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
			// For firmware 1.5, we'll test the FYA parsing code directly
			// This will ensure we cover the firmware 1.5 specific code paths

			// Create test data for FYA pairs
			olpData := []byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66} // 6 FYA pairs
			gaData := []byte{0x41, 0x42, 0x43, 0x44, 0x45, 0x46}
			oppData := []byte{0x41, 0x42, 0x43, 0x44, 0x45, 0x46}

			// Call the parseFYAPairs function directly
			fyaCount := int64(6)
			fyaData := parseFYAPairs(olpData, gaData, oppData, fyaCount)

			// Verify the results
			assert.Len(t, fyaData, 6)
			assert.Contains(t, fyaData[0], "Enabled")
			assert.Contains(t, fyaData[0], "FlashRate:true")
			assert.Contains(t, fyaData[0], "RY:true")
			assert.Contains(t, fyaData[0], "YTrap:true")

			// Test getFYAOffsets for firmware 1.5
			recordSize, olpOffset, gaOffset, oppOffset := getFYAOffsets(0x16)
			assert.Equal(t, CFCMU2rmsLogLengthFYA, recordSize)
			assert.Equal(t, CFYA_OLPOffset, olpOffset)
			assert.Equal(t, CFYA_GaOffset, gaOffset)
			assert.Equal(t, CFYA_OPPOffset, oppOffset)

			// Test getFYACount for firmware 1.5
			count := getFYACount(0x16)
			assert.Equal(t, int64(6), count)

			// Test parseFYAFlags
			redYellowEnable, flashRateFault, yellowTrapDetection, flashRateDetection := parseFYAFlags(fyaData)
			assert.Equal(t, "true", redYellowEnable)
			assert.Equal(t, true, flashRateFault)
			assert.True(t, yellowTrapDetection)
			assert.True(t, flashRateDetection)

			// Test parseFYAFlags with empty data
			redYellowEnable, flashRateFault, yellowTrapDetection, flashRateDetection = parseFYAFlags([]string{})
			assert.Equal(t, "", redYellowEnable)
			assert.Equal(t, false, flashRateFault)
			assert.False(t, yellowTrapDetection)
			assert.False(t, flashRateDetection)
		},
	})

	tests = append(tests, testCase{
		name:       "ValidInput_Firmware2_0_MockFYA",
		numRecords: 1,
		recordSize: CFCMU2rmsLogLength8FYA,
		firmware:   0x21,
		setupFn:    nil,
		wantErr:    false,
		validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
			// For firmware 2.0, we'll test the FYA parsing code directly
			// This will ensure we cover the firmware 2.0 specific code paths

			// Create test data for FYA pairs
			olpData := []byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68} // 8 FYA pairs
			gaData := []byte{0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48}
			oppData := []byte{0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48}

			// Call the parseFYAPairs function directly
			fyaCount := int64(8)
			fyaData := parseFYAPairs(olpData, gaData, oppData, fyaCount)

			// Verify the results
			assert.Len(t, fyaData, 8)
			assert.Contains(t, fyaData[0], "Enabled")
			assert.Contains(t, fyaData[0], "FlashRate:true")
			assert.Contains(t, fyaData[0], "RY:true")
			assert.Contains(t, fyaData[0], "YTrap:true")

			// Test getFYAOffsets for firmware 2.0
			recordSize, olpOffset, gaOffset, oppOffset := getFYAOffsets(0x21)
			assert.Equal(t, CFCMU2rmsLogLength8FYA, recordSize)
			assert.Equal(t, CFYA_OLPOffset2, olpOffset)
			assert.Equal(t, CFYA_GaOffset2, gaOffset)
			assert.Equal(t, CFYA_OPPOffset2, oppOffset)

			// Test getFYACount for firmware 2.0
			count := getFYACount(0x21)
			assert.Equal(t, int64(8), count)

			// Test getFYAOffsets for firmware < 1.5
			recordSize, olpOffset, gaOffset, oppOffset = getFYAOffsets(0x14)
			assert.Equal(t, CFCMU2rmsLogLength, recordSize)
			assert.Equal(t, 0, olpOffset)
			assert.Equal(t, 0, gaOffset)
			assert.Equal(t, 0, oppOffset)

			// Test parseConfigRecord
			byteMsg := createByteMsg(1, CFCMU2rmsLogLength, 0x21)
			offset := int64(HeaderLength + 1)
			record, err := parseConfigRecord(
				byteMsg,
				offset,
				int64(olpOffset),
				int64(gaOffset),
				int64(oppOffset),
				int64(fyaCount),
				int64(0x21),
				httpHeader.GatewayTimezone,
			)
			assert.NoError(t, err)
			assert.NotNil(t, record)
		},
	})

	// Add test cases for MiscByteVariations
	miscByteTestCases := []struct {
		miscByte       byte
		redFaultTiming string
		recurrentPulse bool
	}{
		{0x00, "15", true},
		{0x01, "15", false},
		{0x02, "16", true},
		{0x03, "16", false},
		{0x04, "17", true},
		{0x05, "17", false},
		{0x06, "18", true},
		{0x07, "18", false},
	}

	for _, mtc := range miscByteTestCases {
		miscByte := mtc.miscByte
		redFaultTiming := mtc.redFaultTiming
		recurrentPulse := mtc.recurrentPulse

		tests = append(tests, testCase{
			name:       "MiscByte_" + fmt.Sprintf("0x%02x", miscByte),
			numRecords: 1,
			recordSize: CFCMU2rmsLogLength,
			firmware:   0x10,
			setupFn: func(byteMsg []byte) {
				byteMsg[HeaderLength+1+MiscOffset] = miscByte
				// Recompute the checksum after modifying the data
				byteMsg[len(byteMsg)-1] = computeChecksum(byteMsg)
			},
			wantErr: false,
			validateFn: func(t *testing.T, result *helper.ConfigurationChangeLogRecords) {
				assert.NotNil(t, result)
				assert.Equal(t, redFaultTiming, result.Record[0].RedFaultTiming)
				assert.Equal(t, recurrentPulse, result.Record[0].RecurrentPulse)
			},
		})
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup: Create a message with the test case parameters
			byteMsg := createByteMsg(tt.numRecords, tt.recordSize, tt.firmware)

			// Optional: Apply any setup function
			if tt.setupFn != nil {
				tt.setupFn(byteMsg)
			}

			// Setup: Create a header with the firmware version
			header := &helper.HeaderRecord{FirmwareRevision: int64(tt.firmware)}

			// Execute test
			result, err := (EDICMU2212{}).LogConfiguration(httpHeader, byteMsg, header)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "Expected an error but got none")
				if tt.expectedErr != "" {
					assert.Contains(t, err.Error(), tt.expectedErr,
						"Error message should contain %q", tt.expectedErr)
				}
				assert.Nil(t, result, "Expected nil result when error occurs")
			} else {
				assert.NoError(t, err, "Should not return an error")
				assert.NotNil(t, result, "Should return a non-nil result")

				// Validate the result if a validation function is provided
				if tt.validateFn != nil {
					tt.validateFn(t, result)
				}
			}
		})
	}
}

// Test_parseBitmap_Scenarios tests the parseBitmap function with various inputs
func Test_parseBitmap_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	type testCase struct {
		name           string // Required: descriptive test name
		inputData      []byte // Input: byte array to parse
		size           int    // Input: number of bits to extract
		expectedResult []bool // Expected output: array of boolean values
		expectedNil    bool   // Whether the result should be nil
	}

	// Define test cases
	tests := []testCase{
		{
			name:           "ValidInput",
			inputData:      []byte{0x05}, // Bits 0 and 2 set
			size:           8,
			expectedResult: []bool{true, false, true, false, false, false, false, false},
			expectedNil:    false,
		},
		{
			name:           "ShortInput",
			inputData:      []byte{},
			size:           8,
			expectedResult: nil,
			expectedNil:    true,
		},
		{
			name:      "LargeSize",
			inputData: []byte{0xFF, 0xFF},
			size:      16, // Use 16 instead of 32 to match the data length
			expectedResult: func() []bool {
				result := make([]bool, 16)
				for i := range result {
					result[i] = true
				}
				return result
			}(),
			expectedNil: false,
		},
		{
			name:           "ZeroSize",
			inputData:      []byte{0xFF},
			size:           0,
			expectedResult: []bool{},
			expectedNil:    false,
		},
		{
			name:      "MultipleBytesPartiallySet",
			inputData: []byte{0xAA, 0x55}, // Alternating bits
			size:      16,
			expectedResult: []bool{
				false, true, false, true, false, true, false, true,
				true, false, true, false, true, false, true, false,
			},
			expectedNil: false,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Execute test
			result := parseBitmap(tt.inputData, tt.size)

			// Assert results
			if tt.expectedNil {
				assert.Nil(t, result, "parseBitmap(%v, %d) should return nil", tt.inputData, tt.size)
			} else {
				assert.NotNil(t, result, "parseBitmap(%v, %d) should return a non-nil result", tt.inputData, tt.size)
				assert.Equal(t, tt.expectedResult, result,
					"parseBitmap(%v, %d) should return %v", tt.inputData, tt.size, tt.expectedResult)
			}
		})
	}
}

// Test_parseVirtualChannels_Scenarios tests the parseVirtualChannels function with various inputs
func Test_parseVirtualChannels_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	type testCase struct {
		name        string                                                                        // Required: descriptive test name
		inputData   func() []byte                                                                 // Input: function to create input data
		validateFn  func(redSettings, yellowSettings, greenSettings []helper.VirtualSetting) bool // Function to validate the result
		expectedNil bool                                                                          // Whether the result should be nil
	}

	// Define test cases
	tests := []testCase{
		{
			name: "ValidInput",
			inputData: func() []byte {
				data := make([]byte, 12)
				data[0] = 0x21 // Red, Channel 1
				data[1] = 0x41 // Yellow, Channel 1
				data[2] = 0x61 // Green, Channel 1
				return data
			},
			validateFn: func(redSettings, yellowSettings, greenSettings []helper.VirtualSetting) bool {
				if len(redSettings) != 4 || len(yellowSettings) != 4 || len(greenSettings) != 4 {
					return false
				}

				// Check first virtual channel settings
				if !redSettings[0].Enabled || redSettings[0].SourceChannel != 1 || redSettings[0].SourceColor != "Red" {
					return false
				}
				if !yellowSettings[0].Enabled || yellowSettings[0].SourceChannel != 1 || yellowSettings[0].SourceColor != "Yellow" {
					return false
				}
				if !greenSettings[0].Enabled || greenSettings[0].SourceChannel != 1 || greenSettings[0].SourceColor != "Green" {
					return false
				}

				// Check that other channels are not enabled
				for i := 1; i < 4; i++ {
					if redSettings[i].Enabled || yellowSettings[i].Enabled || greenSettings[i].Enabled {
						return false
					}
				}

				return true
			},
			expectedNil: false,
		},
		{
			name: "ShortInput",
			inputData: func() []byte {
				return make([]byte, 5) // Too short
			},
			validateFn:  nil,
			expectedNil: true,
		},
		{
			name: "NoSource",
			inputData: func() []byte {
				return make([]byte, 12) // All zeros
			},
			validateFn: func(redSettings, yellowSettings, greenSettings []helper.VirtualSetting) bool {
				if len(redSettings) != 4 || len(yellowSettings) != 4 || len(greenSettings) != 4 {
					return false
				}

				// Check that all channels are not enabled
				for i := 0; i < 4; i++ {
					if redSettings[i].Enabled || yellowSettings[i].Enabled || greenSettings[i].Enabled {
						return false
					}
				}

				return true
			},
			expectedNil: false,
		},
		{
			name: "UnknownColor",
			inputData: func() []byte {
				data := make([]byte, 12)
				data[0] = 0x01 // Channel 1, Unknown color (bits 5-6 are 00)
				return data
			},
			validateFn: func(redSettings, yellowSettings, greenSettings []helper.VirtualSetting) bool {
				return redSettings[0].Enabled &&
					redSettings[0].SourceChannel == 1 &&
					redSettings[0].SourceColor == "Unknown"
			},
			expectedNil: false,
		},
		{
			name: "AllVirtualChannels",
			inputData: func() []byte {
				data := make([]byte, 12)
				// Set all 4 virtual channels with different sources
				data[0] = 0x21  // VC1 Red = Ch1 Red
				data[1] = 0x42  // VC1 Yellow = Ch2 Yellow
				data[2] = 0x63  // VC1 Green = Ch3 Green
				data[3] = 0x24  // VC2 Red = Ch4 Red
				data[4] = 0x45  // VC2 Yellow = Ch5 Yellow
				data[5] = 0x66  // VC2 Green = Ch6 Green
				data[6] = 0x27  // VC3 Red = Ch7 Red
				data[7] = 0x48  // VC3 Yellow = Ch8 Yellow
				data[8] = 0x69  // VC3 Green = Ch9 Green
				data[9] = 0x2A  // VC4 Red = Ch10 Red
				data[10] = 0x4B // VC4 Yellow = Ch11 Yellow
				data[11] = 0x6C // VC4 Green = Ch12 Green
				return data
			},
			validateFn: func(redSettings, yellowSettings, greenSettings []helper.VirtualSetting) bool {
				if len(redSettings) != 4 || len(yellowSettings) != 4 || len(greenSettings) != 4 {
					return false
				}

				// Expected channel numbers and colors
				expectedRed := []int{1, 4, 7, 10}
				expectedYellow := []int{2, 5, 8, 11}
				expectedGreen := []int{3, 6, 9, 12}

				// Check all virtual channels
				for i := 0; i < 4; i++ {
					if !redSettings[i].Enabled || redSettings[i].SourceChannel != expectedRed[i] || redSettings[i].SourceColor != "Red" {
						return false
					}
					if !yellowSettings[i].Enabled || yellowSettings[i].SourceChannel != expectedYellow[i] || yellowSettings[i].SourceColor != "Yellow" {
						return false
					}
					if !greenSettings[i].Enabled || greenSettings[i].SourceChannel != expectedGreen[i] || greenSettings[i].SourceColor != "Green" {
						return false
					}
				}

				return true
			},
			expectedNil: false,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup: Create input data
			data := tt.inputData()

			// Execute test
			redSettings, yellowSettings, greenSettings := parseVirtualChannels(data)

			// Assert results
			if tt.expectedNil {
				assert.Nil(t, redSettings, "Red settings should be nil")
				assert.Nil(t, yellowSettings, "Yellow settings should be nil")
				assert.Nil(t, greenSettings, "Green settings should be nil")
			} else {
				assert.NotNil(t, redSettings, "Red settings should not be nil")
				assert.NotNil(t, yellowSettings, "Yellow settings should not be nil")
				assert.NotNil(t, greenSettings, "Green settings should not be nil")

				// Validate the result using the provided validation function
				if tt.validateFn != nil {
					assert.True(t, tt.validateFn(redSettings, yellowSettings, greenSettings),
						"Validation function should return true for the parsed virtual channels")
				}
			}
		})
	}
}

// Test_parseFYAPairs_Scenarios tests the parseFYAPairs function with various inputs
func Test_parseFYAPairs_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	type testCase struct {
		name        string              // Required: descriptive test name
		olpData     []byte              // Input: OLP data
		gaData      []byte              // Input: GA data
		oppData     []byte              // Input: OPP data
		fyaCount    int64               // Input: FYA count
		validateFn  func([]string) bool // Function to validate the result
		expectedNil bool                // Whether the result should be nil
	}

	// Define test cases
	tests := []testCase{
		{
			name:     "ValidInput",
			olpData:  []byte{0x61}, // Enabled, FlashRate, Ch1
			gaData:   []byte{0x41}, // RedYellowEnable, Ch1
			oppData:  []byte{0x41}, // YTrap, Ch1
			fyaCount: int64(1),
			validateFn: func(result []string) bool {
				if len(result) != 1 {
					return false
				}
				return strings.Contains(result[0], "FYA1: Enabled") &&
					strings.Contains(result[0], "FlashRate:true") &&
					strings.Contains(result[0], "RY:true") &&
					strings.Contains(result[0], "YTrap:true")
			},
			expectedNil: false,
		},
		{
			name:        "ShortInput",
			olpData:     []byte{},
			gaData:      []byte{},
			oppData:     []byte{},
			fyaCount:    int64(1),
			validateFn:  nil,
			expectedNil: true,
		},
		{
			name:     "DisabledFYA",
			olpData:  []byte{0x01}, // Disabled, Ch1
			gaData:   []byte{0x01},
			oppData:  []byte{0x01},
			fyaCount: int64(1),
			validateFn: func(result []string) bool {
				if len(result) != 1 {
					return false
				}
				return strings.Contains(result[0], "Disabled") &&
					strings.Contains(result[0], "FlashRate:false") &&
					strings.Contains(result[0], "RY:false") &&
					strings.Contains(result[0], "YTrap:false")
			},
			expectedNil: false,
		},
		{
			name: "MultipleFYAs",
			olpData: func() []byte {
				data := make([]byte, 8)
				for i := 0; i < 8; i++ {
					data[i] = byte(0x20 | (i + 1)) // Enabled, different channels
					if i%2 == 0 {
						data[i] |= 0x40 // Set FlashRate for even FYAs
					}
				}
				return data
			}(),
			gaData: func() []byte {
				data := make([]byte, 8)
				for i := 0; i < 8; i++ {
					data[i] = byte(i + 1)
					if i%3 == 0 {
						data[i] |= 0x40 // Set RedYellowEnable for every third FYA
					}
				}
				return data
			}(),
			oppData: func() []byte {
				data := make([]byte, 8)
				for i := 0; i < 8; i++ {
					data[i] = byte(i + 1)
					if i%4 == 0 {
						data[i] |= 0x40 // Set YTrap for every fourth FYA
					}
				}
				return data
			}(),
			fyaCount: 8,
			validateFn: func(result []string) bool {
				if len(result) != 8 {
					return false
				}

				// Check specific FYAs
				return strings.Contains(result[0], "FYA1: Enabled") &&
					strings.Contains(result[0], "FlashRate:true") &&
					strings.Contains(result[0], "RY:true") &&
					strings.Contains(result[0], "YTrap:true") &&
					strings.Contains(result[1], "FYA2: Enabled") &&
					strings.Contains(result[1], "FlashRate:false") &&
					strings.Contains(result[1], "RY:false") &&
					strings.Contains(result[1], "YTrap:false")
			},
			expectedNil: false,
		},
		{
			name:     "ZeroLTandOPPChannels",
			olpData:  []byte{0x61}, // Enabled, FlashRate, Ch1
			gaData:   []byte{0x00}, // LT Channel 0 (should display as --)
			oppData:  []byte{0x00}, // OPP Channel 0 (should display as --)
			fyaCount: 1,
			validateFn: func(result []string) bool {
				if len(result) != 1 {
					return false
				}
				return strings.Contains(result[0], "LT Ch:--") &&
					strings.Contains(result[0], "OPP Ch:--")
			},
			expectedNil: false,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Execute test
			result := parseFYAPairs(tt.olpData, tt.gaData, tt.oppData, tt.fyaCount)

			// Assert results
			if tt.expectedNil {
				assert.Nil(t, result, "parseFYAPairs should return nil for invalid input")
			} else {
				assert.NotNil(t, result, "parseFYAPairs should return non-nil result")

				// Validate the result using the provided validation function
				if tt.validateFn != nil {
					assert.True(t, tt.validateFn(result),
						"Validation function should return true for the parsed FYA pairs")
				}
			}
		})
	}
}

// We'll use the helperParsePermissives variable defined in edicmu2212.go

// Test_helperParsePermissives_Error tests the error case for helperParsePermissives in parseConfigRecord
func Test_helperParsePermissives_Error(t *testing.T) {
	// Define test case structure according to coverage rules
	type testCase struct {
		name         string // Required: descriptive test name
		setupFn      func() // Optional: setup function for the test
		cleanupFn    func() // Optional: cleanup function for the test
		byteMsg      []byte // Input: byte message
		offset       int64  // Input: offset parameter
		recordSize   int64  // Input: record size parameter
		fyaOLPOffset int64  // Input: FYA OLP offset parameter
		fyaGaOffset  int64  // Input: FYA GA offset parameter
		fyaOPPOffset int64  // Input: FYA OPP offset parameter
		fyaCount     int64  // Input: FYA count parameter
		firmwareRev  int64  // Input: firmware revision parameter
		timezone     string // Input: timezone parameter
		expectedErr  string // Expected error message
	}

	// Define test cases
	tests := []testCase{
		{
			name: "ParsePermissives_Error",
			setupFn: func() {
				// Save original function
				origParsePermissives := helperParsePermissives

				// Override with mock that returns an error
				helperParsePermissives = func(data []byte, channelCount int) ([][]string, error) {
					return nil, fmt.Errorf("not enough bytes supplied to fill channel count: have %d bits (%d bytes), need %d bits for %d channels",
						len(data)*8, len(data), channelCount*(channelCount-1)/2, channelCount)
				}

				// Restore original in cleanup
				t.Cleanup(func() {
					helperParsePermissives = origParsePermissives
				})
			},
			cleanupFn: nil, // Using t.Cleanup instead
			byteMsg: func() []byte {
				// Create a valid message with enough bytes
				byteMsg := make([]byte, HeaderLength+1+CFCMU2rmsLogLength)

				// Set the header and other required fields
				byteMsg[0] = 0x01 // Some valid header values
				byteMsg[1] = 0x01
				byteMsg[2] = 0x01
				byteMsg[3] = 0x10 // Firmware version
				byteMsg[4] = 0x00
				byteMsg[5] = 0x00
				byteMsg[6] = 0x00
				byteMsg[HeaderLength] = 0x01 // 1 record

				// Set valid month, day, year, hour, min, sec for date/time
				byteMsg[HeaderLength+1+ConfigTimeMonOffset] = 0x01
				byteMsg[HeaderLength+1+ConfigTimeDayOffset] = 0x01
				byteMsg[HeaderLength+1+ConfigTimeYearOffset] = 0x20
				byteMsg[HeaderLength+1+ConfigTimeHourOffset] = 0x12
				byteMsg[HeaderLength+1+ConfigTimeMinOffset] = 0x30
				byteMsg[HeaderLength+1+ConfigTimeSecOffset] = 0x00

				return byteMsg
			}(),
			offset:       int64(HeaderLength + 1),
			recordSize:   int64(CFCMU2rmsLogLength),
			fyaOLPOffset: int64(0),
			fyaGaOffset:  int64(0),
			fyaOPPOffset: int64(0),
			fyaCount:     int64(0),
			firmwareRev:  int64(0x10),
			timezone:     "UTC",
			expectedErr:  "not enough bytes",
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Optional: Setup test
			if tt.setupFn != nil {
				tt.setupFn()
			}

			// Execute test
			_, err := parseConfigRecord(
				tt.byteMsg,
				tt.offset,
				tt.fyaOLPOffset,
				tt.fyaGaOffset,
				tt.fyaOPPOffset,
				tt.fyaCount,
				tt.firmwareRev,
				tt.timezone,
			)

			// Assert results
			assert.Error(t, err, "parseConfigRecord should return an error when helperParsePermissives fails")
			assert.Contains(t, err.Error(), tt.expectedErr,
				"Error message should contain %q", tt.expectedErr)

			// Optional: Cleanup test
			if tt.cleanupFn != nil {
				tt.cleanupFn()
			}
		})
	}
}

// Test_ParseCurrentSenseData_Scenarios tests the ParseCurrentSenseData function with various inputs
func Test_ParseCurrentSenseData_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	type testCase struct {
		name        string                                                                                                         // Required: descriptive test name
		setupFn     func() []byte                                                                                                  // Input: function to create and setup the byte message
		offset      int64                                                                                                          // Input: offset parameter
		wantErr     bool                                                                                                           // Whether error is expected
		expectedErr string                                                                                                         // Expected error message if any
		validateFn  func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool // Function to validate the result
	}

	// Define test cases
	tests := []testCase{
		{
			name: "ValidInput",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				byteMsg[CSUROffset] = 0x01
				byteMsg[CSUYOffset] = 0x01
				byteMsg[CSUGOffset] = 0x01
				byteMsg[CSURThreshOffset] = 130 // Scaled: (130*16)-1800 = 280 mA
				byteMsg[CSUYThreshOffset] = 50  // Unscaled
				byteMsg[CSUGThreshOffset] = 50  // Unscaled
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				if len(redEnabled) != 32 || len(yellowEnabled) != 32 || len(greenEnabled) != 32 ||
					len(redThreshold) != 32 || len(yellowThreshold) != 32 || len(greenThreshold) != 32 {
					return false
				}
				// Check that channel 1 is enabled for red and has the correct thresholds
				return redEnabled[0] &&
					redThreshold[0] == 280 &&
					yellowThreshold[0] == 50 &&
					greenThreshold[0] == 50
			},
		},
		{
			name: "ShortInput",
			setupFn: func() []byte {
				return make([]byte, 10) // Too short
			},
			offset:      0,
			wantErr:     true,
			expectedErr: "byte message too short",
			validateFn:  nil,
		},
		{
			name: "ThresholdPadding",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				byteMsg[CSUROffset] = 0x01
				byteMsg[CSURThreshOffset] = 5 // Small value for padding
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				return redThreshold[0] == 5 // Small value for padding
			},
		},
		{
			name: "NoEnableFlags",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// All enable flags are 0
				byteMsg[CSUROffset] = 0x00
				byteMsg[CSUYOffset] = 0x00
				byteMsg[CSUGOffset] = 0x00
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				if len(redEnabled) != 32 {
					return false
				}
				// Check that all channels are disabled
				return !redEnabled[0] && !yellowEnabled[0] && !greenEnabled[0]
			},
		},
		{
			name: "ScalePointBoundary",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Set thresholds at ScalePoint (120)
				byteMsg[CSURThreshOffset] = ScalePoint
				byteMsg[CSUYThreshOffset] = ScalePoint
				byteMsg[CSUGThreshOffset] = ScalePoint
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Check that the threshold is at ScalePoint (120) and not scaled
				return redThreshold[0] == 120 && yellowThreshold[0] == 120 && greenThreshold[0] == 120
			},
		},
		{
			name: "ScalePointAbove",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Set thresholds just above ScalePoint
				byteMsg[CSURThreshOffset] = ScalePoint + 1
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Should be scaled: (121*16)-1800 = 136 mA
				return redThreshold[0] == 136
			},
		},
		{
			name: "HighChannelNumbers",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				byteMsg[CSUROffset+2] = 0x01       // Enable channel 17 (high channel in MS byte)
				byteMsg[CSURThreshOffset+16] = 130 // Set threshold for channel 17
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Channel 17 should be enabled for red
				return redEnabled[16] && redThreshold[16] == 280
			},
		},
		{
			name: "ThresholdFormatting",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Test different threshold values for padding
				byteMsg[CSURThreshOffset] = 5     // Single digit
				byteMsg[CSURThreshOffset+1] = 50  // Double digit
				byteMsg[CSURThreshOffset+2] = 100 // Triple digit
				byteMsg[CSURThreshOffset+3] = 200 // Above ScalePoint, will be scaled to 4 digits
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Check different threshold values
				return redThreshold[0] == 5 && // Single digit
					redThreshold[1] == 50 && // Double digit
					redThreshold[2] == 100 && // Triple digit
					redThreshold[3] == 1400 // Scaled value
			},
		},
		{
			name: "YellowThresholdScaling",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Enable yellow channel for first channel
				byteMsg[CSUYOffset] = 0x01
				// Set different threshold values
				byteMsg[CSUYThreshOffset] = 50    // Below ScalePoint - should remain 50 mA
				byteMsg[CSUYThreshOffset+1] = 120 // At ScalePoint - should remain 120 mA
				byteMsg[CSUYThreshOffset+2] = 121 // Just above ScalePoint - should be scaled to (121*16)-1800 = 136 mA
				byteMsg[CSUYThreshOffset+3] = 200 // Well above ScalePoint - should be scaled to (200*16)-1800 = 1400 mA
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				if len(yellowThreshold) != 32 {
					return false
				}
				// Check scaling for values below, at, and above ScalePoint
				return yellowThreshold[0] == 50 && // Below ScalePoint - not scaled
					yellowThreshold[1] == 120 && // At ScalePoint - not scaled
					yellowThreshold[2] == 136 && // Just above ScalePoint - scaled
					yellowThreshold[3] == 1400 // Well above ScalePoint - scaled
			},
		},
		{
			name: "YellowThresholdPadding",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Enable yellow channel
				byteMsg[CSUYOffset] = 0x01
				// Set thresholds with different digit counts
				byteMsg[CSUYThreshOffset] = 5     // Single digit
				byteMsg[CSUYThreshOffset+1] = 50  // Double digit
				byteMsg[CSUYThreshOffset+2] = 100 // Triple digit
				byteMsg[CSUYThreshOffset+3] = 200 // Scaled to four digits: (200*16)-1800 = 1400
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Check different threshold values
				return yellowThreshold[0] == 5 && // Single digit
					yellowThreshold[1] == 50 && // Double digit
					yellowThreshold[2] == 100 && // Triple digit
					yellowThreshold[3] == 1400 // Scaled value
			},
		},
		{
			name: "YellowThresholdDisabled",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Yellow channel disabled (0x00)
				byteMsg[CSUYOffset] = 0x00
				// Set some threshold value
				byteMsg[CSUYThreshOffset] = 100
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Yellow channel should be disabled but still have a threshold value
				return !yellowEnabled[0] && yellowThreshold[0] == 100
			},
		},
		{
			name: "GreenThresholdScaling",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Enable green channel for first channel
				byteMsg[CSUGOffset] = 0x01
				// Set different threshold values
				byteMsg[CSUGThreshOffset] = 50    // Below ScalePoint - should remain 50 mA
				byteMsg[CSUGThreshOffset+1] = 120 // At ScalePoint - should remain 120 mA
				byteMsg[CSUGThreshOffset+2] = 121 // Just above ScalePoint - should be scaled to (121*16)-1800 = 136 mA
				byteMsg[CSUGThreshOffset+3] = 200 // Well above ScalePoint - should be scaled to (200*16)-1800 = 1400 mA
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				if len(greenThreshold) != 32 {
					return false
				}
				// Check scaling for values below, at, and above ScalePoint
				return greenThreshold[0] == 50 && // Below ScalePoint - not scaled
					greenThreshold[1] == 120 && // At ScalePoint - not scaled
					greenThreshold[2] == 136 && // Just above ScalePoint - scaled
					greenThreshold[3] == 1400 // Well above ScalePoint - scaled
			},
		},
		{
			name: "GreenThresholdPadding",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Enable green channel
				byteMsg[CSUGOffset] = 0x01
				// Set thresholds with different digit counts
				byteMsg[CSUGThreshOffset] = 5     // Single digit
				byteMsg[CSUGThreshOffset+1] = 50  // Double digit
				byteMsg[CSUGThreshOffset+2] = 100 // Triple digit
				byteMsg[CSUGThreshOffset+3] = 200 // Scaled to four digits: (200*16)-1800 = 1400
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Check different threshold values
				return greenThreshold[0] == 5 && // Single digit
					greenThreshold[1] == 50 && // Double digit
					greenThreshold[2] == 100 && // Triple digit
					greenThreshold[3] == 1400 // Scaled value
			},
		},
		{
			name: "GreenThresholdDisabled",
			setupFn: func() []byte {
				byteMsg := make([]byte, CSUGThreshOffset+32)
				// Green channel disabled (0x00)
				byteMsg[CSUGOffset] = 0x00
				// Set some threshold value
				byteMsg[CSUGThreshOffset] = 100
				return byteMsg
			},
			offset:  0,
			wantErr: false,
			validateFn: func(redEnabled, yellowEnabled, greenEnabled []bool, redThreshold, yellowThreshold, greenThreshold []int) bool {
				// Green channel should be disabled but still have a threshold value
				return !greenEnabled[0] && greenThreshold[0] == 100
			},
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		tt := tt // Capture range variable to use in closure
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Setup: Create and setup the byte message
			byteMsg := tt.setupFn()

			// Execute test
			redEnabled, yellowEnabled, greenEnabled, redThreshold, yellowThreshold, greenThreshold, err := ParseCurrentSenseData(byteMsg, tt.offset)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "ParseCurrentSenseData should return an error for invalid input")
				if tt.expectedErr != "" {
					assert.Contains(t, err.Error(), tt.expectedErr,
						"Error message should contain %q", tt.expectedErr)
				}
				assert.Nil(t, redEnabled, "Red enabled flags should be nil when error occurs")
				assert.Nil(t, yellowEnabled, "Yellow enabled flags should be nil when error occurs")
				assert.Nil(t, greenEnabled, "Green enabled flags should be nil when error occurs")
				assert.Nil(t, redThreshold, "Red thresholds should be nil when error occurs")
				assert.Nil(t, yellowThreshold, "Yellow thresholds should be nil when error occurs")
				assert.Nil(t, greenThreshold, "Green thresholds should be nil when error occurs")
			} else {
				assert.NoError(t, err, "ParseCurrentSenseData should not return an error for valid input")
				assert.NotNil(t, redEnabled, "Red enabled flags should not be nil")
				assert.NotNil(t, yellowEnabled, "Yellow enabled flags should not be nil")
				assert.NotNil(t, greenEnabled, "Green enabled flags should not be nil")
				assert.NotNil(t, redThreshold, "Red thresholds should not be nil")
				assert.NotNil(t, yellowThreshold, "Yellow thresholds should not be nil")
				assert.NotNil(t, greenThreshold, "Green thresholds should not be nil")

				// Validate the result using the provided validation function
				if tt.validateFn != nil {
					assert.True(t, tt.validateFn(redEnabled, yellowEnabled, greenEnabled, redThreshold, yellowThreshold, greenThreshold),
						"Validation function should return true for the parsed current sense data")
				}
			}
		})
	}
}
