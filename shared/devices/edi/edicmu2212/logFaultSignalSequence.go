package edicmu2212

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	TraceMaxTimestamp = int64(65530)
	TraceLength       = 20 // The length of each trace record in bytes
	TSOffset          = 0  // Timestamp in the record
	LogNumOffset      = 1  // LogNum in the record
	FaultTypeOffset   = 0  // FaultType in the record

	// Constants for CMU2212_hv signal offset
	RedCMU2Offset    = 2    // Red signal status in the record
	YellowCMU2Offset = 6    // Yellow signal status in the record
	GreenCMU2Offset  = 10   // Green signal status in the record
	CHiCMU2Offset    = 14   // Channel current in the record
	CabCMU2Offset    = 18   // Cabinet status in the record
	ACCMU2Offset     = 19   // AC voltage in the record
	SBCoilcmu        = 0x10 // The bit mask for SB Coil status
)

/*
Byte Message Layout
===================================

+--------------------------------+
|     Header (7 bytes)           |
+--------------------------------+
|     Event count (1 byte)       |
+--------------------------------+
|     Fault Type (1 byte)        |
+--------------------------------+
|     Trace record count (1 byte)|
+--------------------------------+
|     Trace Records              |
|     (20 bytes per record)      |
+--------------------------------+
|     Checksum (1 byte)          |
+--------------------------------+

Detailed Byte Message Layout
===================================

Header (7 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte


[7] Fault Type
[8] record count

Trace Records (20 bytes per record) big-endian
[9-10] Timestamp
[11-14] Red status
[15-18] Yellow Status
[19-22] Green Status
[23-26] Walk Status (if fault type 10 or 130)
[29] EE_SF_RE (SB Coil bit (0x10))
[28] AC Voltage

*/

func (device EDICMU2212) LogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.FaultSignalSequenceRecords, err error) {
	// Check if the message is empty
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	// Validate the checksum
	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// Get number of records (as LogNum) and validate
	numberOfRecords := int(byteMsg[HeaderLength+LogNumOffset])
	startingByte := HeaderLength + LogNumOffset + 1
	byteMessageLength := HeaderLength + LogNumOffset + numberOfRecords*TraceLength + HeaderOffset
	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	// Extract fault type from byte at HeaderLength + 1
	faultTypeByte := byteMsg[HeaderLength+FaultTypeOffset]

	allRecords = &helper.FaultSignalSequenceRecords{
		RawMessage:  byteMsg,
		FaultType:   getFaultType(faultTypeByte),
		DeviceModel: DeviceModel,
	}

	for idx := range numberOfRecords {
		traceBuffer := byteMsg[startingByte+idx*TraceLength : startingByte+(idx+1)*TraceLength]
		record := parseTraceRecord(traceBuffer, header)
		if record.Timestamp > TraceMaxTimestamp {
			record.Timestamp = TraceMaxTimestamp
		}
		allRecords.Records = append(allRecords.Records, *record)
	}

	// adjust time stamps
	helper.NormalizeTimestamps(allRecords) // double-check this to make sure the data is modified in the structure
	allRecords = helper.Performcalcs(allRecords)

	return allRecords, nil
}

func parseTraceRecord(traceBytes []byte, header *helper.HeaderRecord) (record *helper.TraceBuffer) {
	record = &helper.TraceBuffer{
		BufferRawBytes: traceBytes,
		Timestamp:      int64(traceBytes[0])<<8 | int64(traceBytes[1]),
		Reds:           helper.ParseChannelStatus(helper.CombineBytes(traceBytes[RedCMU2Offset:RedCMU2Offset+4]), int(header.MaxChannels)),
		Yellows:        helper.ParseChannelStatus(helper.CombineBytes(traceBytes[YellowCMU2Offset:YellowCMU2Offset+4]), int(header.MaxChannels)),
		Greens:         helper.ParseChannelStatus(helper.CombineBytes(traceBytes[GreenCMU2Offset:GreenCMU2Offset+4]), int(header.MaxChannels)),
		Walks:          helper.ParseChannelStatus(helper.CombineBytes(traceBytes[CHiCMU2Offset:CHiCMU2Offset+4]), int(header.MaxChannels)),
		EE_SF_RE:       traceBytes[CabCMU2Offset]&SBCoilcmu != 0,
		AcVoltage:      int(traceBytes[ACCMU2Offset]),
	}
	return record
}

// getFaultType maps fault type byte to string
func getFaultType(faultTypeByte byte) string {
	switch faultTypeByte {
	case 1:
		return "24Vdc Fault"
	case 2:
		return "12Vdc Fault"
	case 3:
		return "Conflict Fault"
	case 4:
		return "Serial Bus #1 Error"
	case 5:
		return "Serial Bus #3 Error"
	case 6:
		return "CU Frame-62 Latched Flash (LFSA)"
	case 7:
		return "CU Frame-62 Non-Latched Flash (NFSA)"
	case 8:
		return "Diagnostic Fault"
	case 9:
		return "Multiple Indication Fault"
	case 10:
		return "Lack of Signal Fault"
	case 11:
		return "Clearance (Short Yellow) Fault"
	case 12:
		return "Clearance (Skipped Yellow) Fault"
	case 13:
		return "Yellow + Red Clearance Fault"
	case 14:
		return "Field Output Check Fault"
	case 15:
		return "Data Key Absent"
	case 16:
		return "Data Key FCS Error"
	case 17:
		return "Data Key Invalid Parameter Error"
	case 18:
		return "Local Flash"
	case 19:
		return "Circuit Breaker Trip"
	case 20:
		return "AC Line Low Voltage"
	case 22:
		return "HDSP Diagnostic Error"
	case 23:
		return "FYA Flash Rate Fault"
	case 24:
		return "48Vdc Fault"
	case 129:
		return "Recurrent Pulse Conflict Fault"
	case 130:
		return "Recurrent Pulse Lack of Signal Fault"
	case 131:
		return "Recurrent Pulse Multiple Indication Fault"
	case 132:
		return "Configuration Change Fault"
	case 133:
		return "48Vdc Fault"
	case 134:
		return "CU Watchdog Fail"
	default:
		return ""
	}
}
