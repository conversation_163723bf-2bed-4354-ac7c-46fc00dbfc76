package organization

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	onramphelper "synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	GetConnections      func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	CreateOrganization  func(pg connect.DatabaseExecutor, req *CreateAnhUpdateOrganizationRequest) (*Organization, error)
	GetAllOrganizations func(pg connect.DatabaseExecutor) (*[]Organization, error)
	GetOrganization     func(pg connect.DatabaseExecutor, identifier string) (*Organization, error)
	UpdateOrganization  func(pg connect.DatabaseExecutor, identifier string, req *CreateAnhUpdateOrganizationRequest) (*Organization, error)
	DeleteOrganization  func(pg connect.DatabaseExecutor, identifier string) error
}

// This handler will create a new organization
func CreateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request body with validation
		requestBody, err := parseCreatAndUpdateRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create organization using dependency injection
		organization, err := deps.CreateOrganization(pg, requestBody)
		if err != nil {
			logger.Errorf("Error creating organization: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the created organization
		response.CreateSuccessResponse(organization.ToResponse(), w)
	}
}

// This handler will get all avaiable organizations
func GetAllHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get all organizations
		allOrgs, err := deps.GetAllOrganizations(pg)
		if err != nil {
			logger.Errorf("failed to get organizations: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Convert to response format
		responses := make([]OrganizationResponse, len(*allOrgs))
		for i, org := range *allOrgs {
			responses[i] = org.ToResponse()
		}

		response.CreateSuccessResponse(responses, w)
	}
}

// This handler will get a avaiable organization via Identifier
func GetByIdentifierHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate identifier
		vars := mux.Vars(r)
		identifier := vars["identifier"]
		if onramphelper.ValidateIdentifier(identifier) != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get organization
		org, err := deps.GetOrganization(pg, identifier)
		if err != nil {
			if errors.Is(err, ErrOrganizationNotFound) {
				logger.Errorf("Organization not found: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to get organization: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(org.ToResponse(), w)
	}
}

// This handler will update a avaiable organization via Identifier
func UpdateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate identifier
		vars := mux.Vars(r)
		identifier := vars["identifier"]
		if onramphelper.ValidateIdentifier(identifier) != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseCreatAndUpdateRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update organization
		organization, err := deps.UpdateOrganization(pg, identifier, requestBody)
		if err != nil {
			if errors.Is(err, ErrOrganizationNotFound) {
				logger.Errorf("Organization not found: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error updating organization: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(organization.ToResponse(), w)
	}
}

// This hanlder will deleate a avaiable organization via Identifier
func DeleteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate identifier
		vars := mux.Vars(r)
		identifier := vars["identifier"]
		if onramphelper.ValidateIdentifier(identifier) != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete organization
		err = deps.DeleteOrganization(pg, identifier)
		if err != nil {
			if errors.Is(err, ErrOrganizationNotFound) {
				logger.Errorf("Organization not found: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Error deleting organization: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse("", w)
	}
}

// Delete organization
var deleteOrganization = func(pg connect.DatabaseExecutor, identifier string) error {
	// Get timestamp to update
	now := time.Now().UTC() // Use UTC for consistent timestamps

	query := `
		UPDATE {{Organization}}
		SET 
			IsDeleted = true, 
			DeletedAt = $1
		WHERE 
			OrganizationIdentifier = $2 
			AND IsDeleted = false`

	result, err := pg.Exec(query, now, identifier)
	if err != nil {
		logger.Errorf("failed to soft delete organization: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if result != nil {
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("failed to get rows affected: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		if rowsAffected == 0 {
			return ErrOrganizationNotFound
		}
	}

	return nil
}

// Update organization
var updateOrganization = func(pg connect.DatabaseExecutor, identifier string, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
	// Get timestamp to update
	now := time.Now().UTC() // Use UTC for consistent timestamps

	query := `
		UPDATE {{Organization}}
		SET 
			Description = $1, 
			UpdatedAt = $2
		WHERE 
			OrganizationIdentifier = $3 
			AND IsDeleted = false
		RETURNING Id, OrganizationIdentifier, Description, APIKey, CreatedAt, UpdatedAt, DeletedAt, IsDeleted`

	var org Organization
	err := pg.QueryRowStruct(&org, query, req.Description, now, identifier)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrOrganizationNotFound
		}
		logger.Errorf("failed to update organization: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &org, nil
}

// Get organization via identifier
var getOrganizationByIdentifier = func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
	query := `
		SELECT 
			Id,
			OrganizationIdentifier,
			Description,
			APIKey,
			CreatedAt,
			UpdatedAt,
			DeletedAt,
			IsDeleted
		FROM {{Organization}}
		WHERE OrganizationIdentifier = $1 AND IsDeleted = false`

	var org Organization
	err := pg.QueryRowStruct(&org, query, identifier)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrOrganizationNotFound
		}
		logger.Errorf("failed to get organization by identifier: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &org, nil
}

// get all Organizations from pg
var getAllOrganizations = func(pg connect.DatabaseExecutor) (*[]Organization, error) {
	query := `
		SELECT 
			Id, 
			OrganizationIdentifier, 
			Description, 
			APIKey, 
			CreatedAt, 
			UpdatedAt, 
			DeletedAt, 
			IsDeleted
		FROM {{Organization}}
		WHERE IsDeleted = false
		ORDER BY CreatedAt DESC`

	var orgs []Organization
	err := pg.QueryGenericSlice(&orgs, query)
	if err != nil {
		logger.Errorf("failed to get organizations: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &orgs, nil
}

// Insert a new organization to postgres db
var createOrganization = func(pg connect.DatabaseExecutor, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
	// Generate secure UUIDs for identifier and API key
	orgIdentifier := uuid.New().String()
	apiKey := uuid.New().String()
	now := time.Now().UTC() // Use UTC for consistent timestamps

	// Insert into database
	query := `
		INSERT INTO {{Organization}} (
			OrganizationIdentifier,
			Description,
			APIKey, 
			CreatedAt, 
			UpdatedAt, 
			IsDeleted
		)
		VALUES (
			$1, $2, $3, $4, $5, $6
		)
		RETURNING Id, OrganizationIdentifier, Description, APIKey, CreatedAt, UpdatedAt, DeletedAt, IsDeleted`

	var org Organization
	err := pg.QueryRowStruct(&org, query, orgIdentifier, req.Description, apiKey, now, now, false)
	if err != nil {
		logger.Errorf("failed to create organization: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &org, nil
}

// Parses and validates the create and update organization request body
func parseCreatAndUpdateRequest(r *http.Request) (*CreateAnhUpdateOrganizationRequest, error) {
	var req CreateAnhUpdateOrganizationRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}

	// Validate the description is not empty
	if strings.TrimSpace(req.Description) == "" {
		return &req, ErrInvalidDescription
	}

	return &req, nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	CreateHandler = CreateHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		CreateOrganization: createOrganization,
	})
	GetAllHandler = GetAllHandlerWithDeps(HandlerDeps{
		GetConnections:      connect.GetConnections,
		GetAllOrganizations: getAllOrganizations,
	})
	GetByIdentifierHandler = GetByIdentifierHandlerWithDeps(HandlerDeps{
		GetConnections:  connect.GetConnections,
		GetOrganization: getOrganizationByIdentifier,
	})
	UpdateHandler = UpdateHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		UpdateOrganization: updateOrganization,
	})
	DeleteHandler = DeleteHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		DeleteOrganization: deleteOrganization,
	})
)
